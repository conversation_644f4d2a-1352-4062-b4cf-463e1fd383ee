:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode - Blue & White Theme) */
  --color-background: var(--color-white);
  --color-surface: rgba(59, 130, 246, 0.02);
  --color-text: rgba(30, 58, 138, 1);
  --color-text-secondary: rgba(59, 130, 246, 0.7);
  --color-primary: rgba(59, 130, 246, 1);
  --color-primary-hover: rgba(37, 99, 235, 1);
  --color-primary-active: rgba(29, 78, 216, 1);
  --color-secondary: rgba(59, 130, 246, 0.1);
  --color-secondary-hover: rgba(59, 130, 246, 0.15);
  --color-secondary-active: rgba(59, 130, 246, 0.2);
  --color-border: rgba(59, 130, 246, 0.2);
  --color-btn-primary-text: var(--color-white);
  --color-card-border: rgba(59, 130, 246, 0.15);
  --color-card-border-inner: rgba(59, 130, 246, 0.1);
  --color-error: var(--color-red-500);
  --color-success: rgba(34, 197, 94, 1);
  --color-warning: var(--color-orange-500);
  --color-info: rgba(59, 130, 246, 0.8);
  --color-focus-ring: rgba(59, 130, 246, 0.3);
  --color-select-caret: rgba(30, 58, 138, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Blue & White Theme - Override Dark Mode) */
    --color-background: var(--color-white);
    --color-surface: rgba(59, 130, 246, 0.02);
    --color-text: rgba(30, 58, 138, 1);
    --color-text-secondary: rgba(59, 130, 246, 0.7);
    --color-primary: rgba(59, 130, 246, 1);
    --color-primary-hover: rgba(37, 99, 235, 1);
    --color-primary-active: rgba(29, 78, 216, 1);
    --color-secondary: rgba(59, 130, 246, 0.1);
    --color-secondary-hover: rgba(59, 130, 246, 0.15);
    --color-secondary-active: rgba(59, 130, 246, 0.2);
    --color-border: rgba(59, 130, 246, 0.2);
    --color-error: var(--color-red-500);
    --color-success: rgba(34, 197, 94, 1);
    --color-warning: var(--color-orange-500);
    --color-info: rgba(59, 130, 246, 0.8);
    --color-focus-ring: rgba(59, 130, 246, 0.3);
    --color-btn-primary-text: var(--color-white);
    --color-card-border: rgba(59, 130, 246, 0.15);
    --color-card-border-inner: rgba(59, 130, 246, 0.1);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Blue & White Theme - Override Dark Mode) */
  --color-background: var(--color-white);
  --color-surface: rgba(59, 130, 246, 0.02);
  --color-text: rgba(30, 58, 138, 1);
  --color-text-secondary: rgba(59, 130, 246, 0.7);
  --color-primary: rgba(59, 130, 246, 1);
  --color-primary-hover: rgba(37, 99, 235, 1);
  --color-primary-active: rgba(29, 78, 216, 1);
  --color-secondary: rgba(59, 130, 246, 0.1);
  --color-secondary-hover: rgba(59, 130, 246, 0.15);
  --color-secondary-active: rgba(59, 130, 246, 0.2);
  --color-border: rgba(59, 130, 246, 0.2);
  --color-error: var(--color-red-500);
  --color-success: rgba(34, 197, 94, 1);
  --color-warning: var(--color-orange-500);
  --color-info: rgba(59, 130, 246, 0.8);
  --color-focus-ring: rgba(59, 130, 246, 0.3);
  --color-btn-primary-text: var(--color-white);
  --color-card-border: rgba(59, 130, 246, 0.15);
  --color-card-border-inner: rgba(59, 130, 246, 0.1);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: #000000;  /* Black text for input values */
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  color: #000000;  /* Black text for textarea values */
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
  color: #000000;  /* Black text for select values */
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Medical Application Specific Styles */

/* Override design system colors for medical dark blue theme */
/* Medical Theme Color Variables - Blue & White Theme */
:root {
  --medical-primary: #2563eb;        /* Blue primary */
  --medical-secondary: #3b82f6;      /* Lighter blue */
  --medical-accent: #1d4ed8;         /* Darker blue accent */
  --medical-light: #64748b;          /* Gray for secondary text */
  --medical-dark: #1e40af;           /* Dark blue */
  --medical-success: #10b981;        /* Keep green for success */
  --medical-warning: #f59e0b;        /* Keep amber for warning */
  --medical-danger: #ef4444;         /* Keep red for danger */
  --medical-text-light: #1f2937;     /* Dark gray/black for text */
  --medical-bg-dark: #ffffff;        /* White background */
  --medical-bg-card: #f8fafc;        /* Very light blue-gray for cards */
  --medical-border: #e2e8f0;         /* Light gray border */
}

/* Base Overrides */
html {
  background: linear-gradient(135deg, var(--medical-bg-dark) 0%, var(--medical-primary) 100%);
  min-height: 100vh;
}

body {
  background: transparent;
  color: var(--medical-text-light);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Header Styles */
.header {
  background: var(--medical-primary);
  border-bottom: 2px solid var(--medical-accent);
  padding: var(--space-24) 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-title {
  color: #ffffff;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  text-align: center;
}

.header-subtitle {
  color: #ffffff;
  font-size: var(--font-size-lg);
  text-align: center;
  margin: var(--space-8) 0 0 0;
  opacity: 0.95;
}

/* Main Layout */
.main {
  padding: var(--space-32) 0;
  min-height: calc(100vh - 200px);
}

.app-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-32);
  align-items: start;
}

@media (max-width: 1024px) {
  .app-grid {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }
}

/* Section Headers */
.section-header {
  margin-bottom: var(--space-24);
  text-align: center;
}

.section-header h2 {
  color: var(--medical-text-light);
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-8);
}

.section-header p {
  color: var(--medical-light);
  opacity: 0.8;
}

/* Form Styling */
.prediction-form {
  background: var(--medical-bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--medical-border);
}

.form-category {
  margin-bottom: var(--space-20);
  border: 1px solid var(--medical-border);
  border-radius: var(--radius-base);
  overflow: hidden;
  background: rgba(30, 64, 175, 0.1);
}

.category-header {
  background: var(--medical-secondary);
  padding: var(--space-16);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color var(--duration-fast);
}

.category-header:hover {
  background: var(--medical-accent);
}

.category-header h3 {
  color: #ffffff;
  font-size: var(--font-size-lg);
  margin: 0;
  font-weight: var(--font-weight-semibold);
}

.toggle-icon {
  color: #ffffff;
  font-size: var(--font-size-lg);
  transition: transform var(--duration-normal);
}

.category-header.collapsed .toggle-icon {
  transform: rotate(-90deg);
}

.category-content {
  padding: var(--space-20);
  border-top: 1px solid var(--medical-border);
}

.category-content.collapsed {
  display: none;
}

/* Form Controls Override - Blue & White Theme */
.form-control {
  background: var(--medical-bg-dark);
  border: 1px solid var(--medical-border);
  color: #000000;  /* Black text for input values */
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-md);
}

.form-control:focus {
  border-color: var(--medical-accent);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

.form-control::placeholder {
  color: var(--medical-light);
  opacity: 0.6;
}

select.form-control {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  color: #000000;  /* Black text for select values */
}

.form-label {
  color: var(--medical-text-light);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-6);
  font-size: var(--font-size-sm);
}

.help-text {
  color: var(--medical-light);
  font-size: var(--font-size-xs);
  opacity: 0.7;
  margin-top: var(--space-4);
  display: block;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

/* Button Overrides */
.btn--primary {
  background: var(--medical-accent);
  color: #ffffff;
  border: none;
  font-weight: var(--font-weight-semibold);
}

.btn--primary:hover {
  background: var(--medical-secondary);
  transform: translateY(-1px);
}

.btn--secondary {
  background: var(--medical-border);
  color: var(--medical-text-light);
  border: 1px solid var(--medical-border);
}

.btn--secondary:hover {
  background: var(--medical-secondary);
  border-color: var(--medical-secondary);
}

.btn--outline {
  background: transparent;
  color: var(--medical-accent);
  border: 1px solid var(--medical-accent);
}

.btn--outline:hover {
  background: var(--medical-accent);
  color: var(--medical-text-light);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--space-16);
  justify-content: center;
  margin-top: var(--space-32);
  padding-top: var(--space-24);
  border-top: 1px solid var(--medical-border);
}

@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }
}

/* Results Section */
.results-section {
  background: var(--medical-bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--medical-border);
}

/* Card Overrides */
.card {
  background: rgba(30, 64, 175, 0.1);
  border: 1px solid var(--medical-border);
  margin-bottom: var(--space-20);
}

.card__header {
  background: var(--medical-secondary);
  border-bottom: 1px solid var(--medical-border);
}

.card__header h3 {
  color: #ffffff;
  margin: 0;
}

.card__body {
  color: var(--medical-text-light);
}

/* Risk Display */
.risk-display {
  display: flex;
  align-items: center;
  gap: var(--space-24);
}

.risk-gauge-container {
  flex-shrink: 0;
}

.risk-details {
  flex: 1;
}

.prediction-result h4 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-8);
  color: var(--medical-text-light);
}

.prediction-result p {
  color: var(--medical-light);
  opacity: 0.8;
  margin: 0;
}

/* Probability Bars */
.probability-bars {
  margin-top: var(--space-20);
}

.prob-bar {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  margin-bottom: var(--space-12);
}

.prob-bar span:first-child {
  min-width: 80px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.prob-bar span:last-child {
  min-width: 40px;
  font-size: var(--font-size-sm);
  text-align: right;
}

.prob-bar-bg {
  flex: 1;
  height: 8px;
  background: var(--medical-border);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.prob-bar-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--duration-normal);
}

.prob-bar-fill.benign {
  background: var(--medical-success);
}

.prob-bar-fill.malignant {
  background: var(--medical-danger);
}

/* Clinical Interpretation */
.interpretation-card {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--medical-success);
}

.interpretation-card.malignant {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--medical-danger);
}

#interpretationTitle {
  color: var(--medical-success);
  margin-bottom: var(--space-12);
}

.malignant #interpretationTitle {
  color: var(--medical-danger);
}

.recommendations h5 {
  color: var(--medical-text-light);
  margin: var(--space-16) 0 var(--space-8) 0;
}

.recommendations ul {
  list-style: none;
  padding: 0;
}

.recommendations li {
  background: rgba(30, 64, 175, 0.1);
  padding: var(--space-8) var(--space-12);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-6);
  border-left: 3px solid var(--medical-accent);
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-20);
  margin: var(--space-24) 0;
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

.chart-container {
  background: #f5f7fa; /* lighter neutral background */
  border-radius: var(--radius-base);
  padding: var(--space-16);
}

/* Model Performance */
.model-info-card {
  background: rgba(30, 64, 175, 0.1);
  border: 1px solid var(--medical-accent);
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-16);
  margin-bottom: var(--space-16);
}

.metric {
  display: flex;
  flex-direction: column;
  text-align: center;
  padding: var(--space-12);
  background: rgba(30, 64, 175, 0.1);
  border-radius: var(--radius-base);
  border: 1px solid var(--medical-border);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--medical-light);
  margin-bottom: var(--space-4);
}

.metric-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--medical-accent);
}

.model-disclaimer {
  font-size: var(--font-size-sm);
  color: var(--medical-light);
  opacity: 0.8;
  line-height: 1.5;
  padding: var(--space-12);
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--radius-base);
  border-left: 3px solid var(--medical-danger);
  margin: 0;
}

/* Export Actions */
.export-actions {
  display: flex;
  gap: var(--space-16);
  justify-content: center;
  margin-top: var(--space-24);
  padding-top: var(--space-20);
  border-top: 1px solid var(--medical-border);
  margin-bottom: var(--space-24); /* add gap before model performance */
}

@media (max-width: 768px) {
  .export-actions {
    flex-direction: column;
  }
}

/* Loading States */
.btn-loading {
  display: none;
}

.btn.loading .btn-text {
  display: none;
}

.btn.loading .btn-loading {
  display: inline;
}

.btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.text-success {
  color: var(--medical-success) !important;
}

.text-danger {
  color: var(--medical-danger) !important;
}

.text-warning {
  color: var(--medical-warning) !important;
}

/* Status Indicators */
.status--success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--medical-success);
  border-color: var(--medical-success);
}

.status--error {
  background: rgba(239, 68, 68, 0.2);
  color: var(--medical-danger);
  border-color: var(--medical-danger);
}

.status--warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--medical-warning);
  border-color: var(--medical-warning);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-title {
    font-size: var(--font-size-2xl);
  }
  
  .header-subtitle {
    font-size: var(--font-size-base);
  }
  
  .app-grid {
    padding: 0 var(--space-16);
  }
  
  .prediction-form,
  .results-section {
    padding: var(--space-16);
  }
  
  .risk-display {
    flex-direction: column;
    text-align: center;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Print Styles */
@media print {
  .header, .form-actions, .export-actions {
    display: none;
  }
  
  .app-grid {
    grid-template-columns: 1fr;
  }
  
  .results-section {
    background: white;
    color: black;
    box-shadow: none;
  }
}

/* Focus Styles for Accessibility */
.form-control:focus-visible,
.btn:focus-visible,
.category-header:focus-visible {
  outline: 2px solid var(--medical-accent);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  .form-control {
    border-width: 2px;
  }
  
  .btn {
    border-width: 2px;
  }
}