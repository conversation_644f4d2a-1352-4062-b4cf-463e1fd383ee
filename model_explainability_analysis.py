#!/usr/bin/env python3
"""
Ovarian Mass Classification - Model Explainability Analysis
=========================================================

Comprehensive explainability analysis for the trained machine learning models
using various interpretation techniques including SHAP, LIME, and feature importance.

Author: AI Assistant
Date: 2025-08-07
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from pathlib import Path
import pickle
import joblib

# Core ML libraries
from sklearn.inspection import permutation_importance
from sklearn.metrics import roc_auc_score, accuracy_score
from sklearn.preprocessing import LabelEncoder

# Explainability libraries
import shap
try:
    import lime
    import lime.lime_tabular
    LIME_AVAILABLE = True
except ImportError:
    print("LIME not available. Install with: pip install lime")
    LIME_AVAILABLE = False

# PyTorch availability check
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Import the main pipeline class
from ovarian_mass_ml_pipeline import OvarianMassClassifier

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

class ModelExplainabilityAnalysis:
    """
    Comprehensive explainability analysis for ovarian mass classification models.
    """
    
    def __init__(self, pipeline_instance=None):
        """
        Initialize the explainability analysis.
        
        Args:
            pipeline_instance: Instance of OvarianMassClassifier with trained models
        """
        self.pipeline = pipeline_instance
        self.explainers = {}
        self.feature_importance_results = {}
        self.shap_values = {}
        self.lime_explanations = {}
        
        # Set up plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
    def setup_pipeline(self):
        """
        Set up and run the pipeline if not provided.
        """
        if self.pipeline is None:
            print("Setting up ML pipeline...")
            self.pipeline = OvarianMassClassifier(
                data_path='combined_ovarian_dataset.csv',
                random_state=42
            )
            # Run the complete pipeline
            self.pipeline.run_complete_pipeline()
        
        return self
    
    def analyze_feature_importance(self):
        """
        Analyze feature importance using multiple methods:
        1. Built-in feature importance (for tree-based models)
        2. Permutation importance
        3. Coefficient analysis (for linear models)
        """
        print("=" * 80)
        print("FEATURE IMPORTANCE ANALYSIS")
        print("=" * 80)
        
        for model_name, model in self.pipeline.models.items():
            print(f"\n📊 Analyzing {model_name}...")
            
            # Initialize results for this model
            self.feature_importance_results[model_name] = {}
            
            try:
                # Handle different model structures
                if model_name == 'MLP':
                    # MLP is stored as dict with 'model' and 'scaler' keys
                    if isinstance(model, dict):
                        print(f"  ⚠️ {model_name} uses custom structure, computing permutation importance only")
                        
                        # Create MLP wrapper for permutation importance
                        class MLPWrapper:
                            def __init__(self, mlp_dict, feature_names):
                                self.mlp_model = mlp_dict['model']
                                self.scaler = mlp_dict['scaler']
                                self.feature_names = feature_names
                            
                            def fit(self, X, y):
                                # Dummy fit method required by sklearn
                                return self
                            
                            def predict_proba(self, X):
                                try:
                                    # Convert to DataFrame if numpy array
                                    if isinstance(X, np.ndarray):
                                        X_df = pd.DataFrame(X, columns=self.feature_names)
                                    else:
                                        X_df = X.copy()
                                    
                                    # Match MLP training preprocessing: scale only numeric columns
                                    X_scaled = X_df.copy()
                                    numeric_cols = X_df.select_dtypes(include=[np.number]).columns
                                    
                                    if len(numeric_cols) > 0:
                                        X_scaled[numeric_cols] = self.scaler.transform(X_df[numeric_cols])
                                    
                                    # Convert to tensor and predict (use all features)
                                    if TORCH_AVAILABLE:
                                        X_tensor = torch.FloatTensor(X_scaled.values)
                                        self.mlp_model.eval()
                                        with torch.no_grad():
                                            proba_pos = self.mlp_model(X_tensor).cpu().numpy().flatten()
                                        proba_neg = 1 - proba_pos
                                        return np.column_stack([proba_neg, proba_pos])
                                    else:
                                        n_samples = X_df.shape[0]
                                        return np.column_stack([np.full(n_samples, 0.5), np.full(n_samples, 0.5)])
                                except Exception as e:
                                    print(f"      Error in MLP prediction: {e}")
                                    n_samples = X_df.shape[0] if hasattr(X_df, 'shape') else len(X)
                                    return np.column_stack([np.full(n_samples, 0.5), np.full(n_samples, 0.5)])
                            
                            def predict(self, X):
                                proba = self.predict_proba(X)
                                return (proba[:, 1] > 0.5).astype(int)
                        
                        # Create wrapper and compute permutation importance
                        mlp_wrapper = MLPWrapper(model, self.pipeline.feature_names)
                        
                        print(f"  🔄 Calculating permutation importance for MLP...")
                        
                        # Use all features for MLP permutation importance (like in training)
                        perm_importance = permutation_importance(
                            mlp_wrapper, self.pipeline.X_val, self.pipeline.y_val,
                            n_repeats=5, random_state=42, scoring='roc_auc', n_jobs=1
                        )
                        
                        perm_df = pd.DataFrame({
                            'feature': self.pipeline.feature_names,
                            'importance_mean': perm_importance.importances_mean,
                            'importance_std': perm_importance.importances_std
                        }).sort_values('importance_mean', ascending=False)
                        
                        self.feature_importance_results[model_name]['permutation'] = perm_df
                        
                        print(f"  ✓ Permutation importance calculated for MLP")
                        print(f"    Top 5 features:")
                        for idx, row in perm_df.head().iterrows():
                            print(f"      {row['feature']}: {row['importance_mean']:.4f} (±{row['importance_std']:.4f})")
                        
                        continue
                elif not hasattr(model, 'named_steps'):
                    print(f"  ❌ {model_name} does not have pipeline structure")
                    continue
                
                # Method 1: Built-in feature importance (for tree-based models)
                if hasattr(model.named_steps['classifier'], 'feature_importances_'):
                    importances = model.named_steps['classifier'].feature_importances_
                    feature_names = self.get_feature_names_after_preprocessing(model)
                    
                    importance_df = pd.DataFrame({
                        'feature': feature_names,
                        'importance': importances
                    }).sort_values('importance', ascending=False)
                    
                    self.feature_importance_results[model_name]['built_in'] = importance_df
                    
                    print(f"  ✓ Built-in feature importance calculated")
                    print(f"    Top 5 features:")
                    for idx, row in importance_df.head().iterrows():
                        print(f"      {row['feature']}: {row['importance']:.4f}")
                
                # Method 2: Coefficient analysis (for linear models)
                elif hasattr(model.named_steps['classifier'], 'coef_'):
                    coef = model.named_steps['classifier'].coef_[0]
                    feature_names = self.get_feature_names_after_preprocessing(model)
                    
                    coef_df = pd.DataFrame({
                        'feature': feature_names,
                        'coefficient': coef,
                        'abs_coefficient': np.abs(coef)
                    }).sort_values('abs_coefficient', ascending=False)
                    
                    self.feature_importance_results[model_name]['coefficients'] = coef_df
                    
                    print(f"  ✓ Coefficient analysis completed")
                    print(f"    Top 5 features by absolute coefficient:")
                    for idx, row in coef_df.head().iterrows():
                        print(f"      {row['feature']}: {row['coefficient']:.4f}")
                
                # Method 3: Permutation importance (for all models except MLP for now)
                if model_name != 'MLP':
                    print(f"  🔄 Calculating permutation importance...")
                    
                    # Use validation set for permutation importance
                    perm_importance = permutation_importance(
                        model, self.pipeline.X_val, self.pipeline.y_val,
                        n_repeats=10, random_state=42, scoring='roc_auc', n_jobs=-1
                    )
                    
                    feature_names = self.get_feature_names_after_preprocessing(model)
                    perm_df = pd.DataFrame({
                        'feature': feature_names,
                        'importance_mean': perm_importance.importances_mean,
                        'importance_std': perm_importance.importances_std
                    }).sort_values('importance_mean', ascending=False)
                    
                    self.feature_importance_results[model_name]['permutation'] = perm_df
                    
                    print(f"  ✓ Permutation importance calculated")
                    print(f"    Top 5 features:")
                    for idx, row in perm_df.head().iterrows():
                        print(f"      {row['feature']}: {row['importance_mean']:.4f} (±{row['importance_std']:.4f})")
                    
            except Exception as e:
                print(f"  ❌ Error analyzing {model_name}: {str(e)}")
        
        return self
    
    def setup_shap_analysis(self):
        """
        Set up SHAP explainers for different model types.
        """
        print("\n" + "=" * 80)
        print("SHAP ANALYSIS SETUP")
        print("=" * 80)
        
        for model_name, model in self.pipeline.models.items():
            print(f"\n🔧 Setting up SHAP for {model_name}...")
            
            try:
                # Handle different model structures
                if model_name == 'MLP':
                    # MLP is stored as dict with 'model' and 'scaler' keys
                    if isinstance(model, dict) and 'model' in model and 'scaler' in model:
                        print(f"  🔧 Setting up KernelExplainer for PyTorch MLP...")
                        
                        # Create MLP prediction wrapper
                        def mlp_predict_wrapper(X_df):
                            """Wrapper function for MLP predictions"""
                            try:
                                # Convert to DataFrame if numpy array
                                if isinstance(X_df, np.ndarray):
                                    X_df = pd.DataFrame(X_df, columns=self.pipeline.feature_names)
                                
                                # Ensure X_df has all the features that the MLP was trained on
                                # Handle missing categorical features by setting them to 0 (most common category)
                                X_processed = X_df.copy()
                                
                                # Add missing categorical features if they don't exist
                                if 'Age_group' not in X_processed.columns and 'AGE' in X_processed.columns:
                                    X_processed['Age_group'] = pd.cut(X_processed['AGE'],
                                                                     bins=[0, 30, 40, 50, 60, 100],
                                                                     labels=[0, 1, 2, 3, 4])
                                    X_processed['Age_group'] = X_processed['Age_group'].astype(float)
                                
                                if 'CA125_risk_category' not in X_processed.columns and 'CA125 (U/mL)' in X_processed.columns:
                                    X_processed['CA125_risk_category'] = pd.cut(X_processed['CA125 (U/mL)'],
                                                                               bins=[0, 35, 200, 1000, float('inf')],
                                                                               labels=[0, 1, 2, 3])
                                    X_processed['CA125_risk_category'] = X_processed['CA125_risk_category'].astype(float)
                                
                                # Fill any NaN values in categorical features with 0
                                if 'Age_group' in X_processed.columns:
                                    X_processed['Age_group'] = X_processed['Age_group'].fillna(0)
                                if 'CA125_risk_category' in X_processed.columns:
                                    X_processed['CA125_risk_category'] = X_processed['CA125_risk_category'].fillna(0)
                                
                                # Match the MLP training preprocessing exactly:
                                # Scale only numeric columns, keep categorical as is
                                X_scaled = X_processed.copy()
                                
                                # Get the numeric columns that were used during training
                                # The scaler was fitted on all numeric columns from the training data
                                training_numeric_cols = [col for col in self.pipeline.feature_names 
                                                       if col in self.pipeline.X_train.select_dtypes(include=[np.number]).columns]
                                
                                # Ensure we have all the numeric columns that the scaler expects
                                for col in training_numeric_cols:
                                    if col not in X_scaled.columns:
                                        # Add missing numeric columns with median values
                                        if col in self.pipeline.X_train.columns:
                                            X_scaled[col] = self.pipeline.X_train[col].median()
                                        else:
                                            X_scaled[col] = 0.0
                                
                                # Select only the numeric columns that were used during training
                                numeric_cols_for_scaling = [col for col in training_numeric_cols if col in X_scaled.columns]
                                
                                if len(numeric_cols_for_scaling) > 0:
                                    X_scaled[numeric_cols_for_scaling] = model['scaler'].transform(X_scaled[numeric_cols_for_scaling])
                                
                                # Ensure we have all features in the exact same order as training
                                # Reorder columns to match the training feature order
                                X_final = X_scaled.reindex(columns=self.pipeline.feature_names, fill_value=0.0)
                                
                                # Convert to tensor using ALL features (like in training)
                                if TORCH_AVAILABLE:
                                    X_tensor = torch.FloatTensor(X_final.values)
                                    model['model'].eval()
                                    with torch.no_grad():
                                        proba = model['model'](X_tensor).cpu().numpy().flatten()
                                    return proba
                                else:
                                    return np.zeros(X_df.shape[0])
                            except Exception as e:
                                print(f"      Error in MLP prediction: {e}")
                                return np.zeros(X_df.shape[0] if hasattr(X_df, 'shape') else len(X_df))
                        
                        # Get background data for KernelExplainer - use ALL features (like in training)
                        background_sample = self.pipeline.X_train.sample(n=min(50, len(self.pipeline.X_train)), random_state=42)
                        
                        explainer = shap.KernelExplainer(
                            mlp_predict_wrapper, 
                            background_sample
                        )
                        print(f"  ✓ KernelExplainer initialized for PyTorch MLP (all {len(self.pipeline.feature_names)} features)")
                        
                        self.explainers[model_name] = explainer
                        continue  # Skip to next model after successful MLP setup
                    else:
                        print(f"  ❌ Invalid MLP structure")
                        continue
                elif not hasattr(model, 'named_steps'):
                    print(f"  ❌ {model_name} does not have pipeline structure")
                    continue
                
                # Get preprocessed training data for background (for pipeline-based models)
                X_train_processed = model.named_steps['preprocessor'].transform(self.pipeline.X_train)
                X_train_processed = model.named_steps['preprocessor'].transform(self.pipeline.X_train)
                
                # Choose appropriate SHAP explainer based on model type
                if 'Random Forest' in model_name or 'XGBoost' in model_name:
                    # Tree explainer for tree-based models
                    explainer = shap.TreeExplainer(
                        model.named_steps['classifier'], 
                        X_train_processed
                    )
                    print(f"  ✓ TreeExplainer initialized")
                    
                elif 'LightGBM' in model_name:
                    # For LightGBM TreeExplainer - try without check_additivity first
                    try:
                        explainer = shap.TreeExplainer(
                            model.named_steps['classifier'], 
                            X_train_processed
                        )
                        print(f"  ✓ TreeExplainer initialized")
                    except Exception as e:
                        print(f"    ⚠️ TreeExplainer failed: {str(e)}")
                        # Fallback to simplified TreeExplainer
                        try:
                            explainer = shap.TreeExplainer(model.named_steps['classifier'])
                            print(f"  ✓ TreeExplainer initialized (simplified)")
                        except Exception as e2:
                            print(f"    ⚠️ Simplified TreeExplainer also failed: {str(e2)}")
                            # Final fallback to KernelExplainer
                            background_sample = shap.sample(X_train_processed, 50)
                            explainer = shap.KernelExplainer(
                                lambda x: model.predict_proba(x)[:, 1], 
                                background_sample
                            )
                            print(f"  ✓ KernelExplainer fallback initialized")
                    
                elif 'Logistic' in model_name:
                    # Linear explainer for linear models
                    explainer = shap.LinearExplainer(
                        model.named_steps['classifier'], 
                        X_train_processed
                    )
                    print(f"  ✓ LinearExplainer initialized")
                    
                else:
                    # KernelExplainer for other models (SVM, Neural Networks)
                    # Use a sample for background to speed up computation
                    background_sample = shap.sample(X_train_processed, 100)
                    
                    # Create a wrapper function that handles the pipeline properly
                    def model_predict_wrapper(X):
                        try:
                            # First try predict_proba
                            proba = model.predict_proba(X)
                            # Return just the positive class probability
                            return proba[:, 1]
                        except Exception:
                            try:
                                # Fallback to predict
                                pred = model.predict(X)
                                return pred.astype(float)
                            except Exception:
                                # Last resort - return zeros
                                return np.zeros(X.shape[0])
                    
                    explainer = shap.KernelExplainer(
                        model_predict_wrapper, background_sample
                    )
                    print(f"  ✓ KernelExplainer initialized")
                
                # Store the explainer for pipeline-based models
                self.explainers[model_name] = explainer
                
            except Exception as e:
                print(f"  ❌ Error setting up SHAP for {model_name}: {str(e)}")
                # Fallback to KernelExplainer only for pipeline-based models
                if hasattr(model, 'named_steps'):
                    try:
                        X_train_processed = model.named_steps['preprocessor'].transform(self.pipeline.X_train)
                        background_sample = shap.sample(X_train_processed, 50)
                        
                        # Create a robust wrapper function for the full pipeline
                        def pipeline_predict_wrapper(X):
                            try:
                                # First try predict_proba and return positive class probability
                                proba = model.predict_proba(X)
                                return proba[:, 1]
                            except Exception:
                                try:
                                    # Fallback to predict
                                    pred = model.predict(X)
                                    return pred.astype(float)
                                except Exception:
                                    # Last resort - return zeros
                                    return np.zeros(X.shape[0])
                        
                        explainer = shap.KernelExplainer(
                            pipeline_predict_wrapper, background_sample
                        )
                        self.explainers[model_name] = explainer
                        print(f"  ✓ Fallback KernelExplainer initialized")
                    except Exception as fallback_error:
                        print(f"  ❌ Could not initialize any explainer for {model_name}: {fallback_error}")
                else:
                    print(f"  ❌ Could not initialize any explainer for {model_name}: {str(e)}")
        
        return self
    
    def calculate_shap_values(self, sample_size=100):
        """
        Calculate SHAP values for the validation set.
        
        Args:
            sample_size (int): Number of samples to analyze (to manage computation time)
        """
        print("\n" + "=" * 80)
        print("CALCULATING SHAP VALUES")
        print("=" * 80)
        
        # Sample validation data for analysis
        if len(self.pipeline.X_val) > sample_size:
            sample_indices = np.random.choice(
                len(self.pipeline.X_val), size=sample_size, replace=False
            )
            X_sample = self.pipeline.X_val.iloc[sample_indices]
            y_sample = self.pipeline.y_val[sample_indices]
        else:
            X_sample = self.pipeline.X_val
            y_sample = self.pipeline.y_val
            sample_indices = np.arange(len(X_sample))
        
        print(f"📊 Analyzing SHAP values for {len(X_sample)} samples...")
        
        for model_name, explainer in self.explainers.items():
            print(f"\n🔄 Calculating SHAP values for {model_name}...")
            
            try:
                model = self.pipeline.models[model_name]
                
                # Handle different model structures
                if model_name == 'MLP':
                    # MLP uses custom structure - use all features (like in training)
                    if isinstance(model, dict) and 'model' in model:
                        X_processed = X_sample  # Use all features for MLP
                        print(f"  ⚠️ Using all features for MLP ({X_processed.shape[1]} features)")
                    else:
                        print(f"  ❌ Invalid MLP structure")
                        continue
                elif not hasattr(model, 'named_steps'):
                    print(f"  ❌ {model_name} does not have pipeline structure")
                    continue
                else:
                    X_processed = model.named_steps['preprocessor'].transform(X_sample)
                
                if 'Random Forest' in model_name or 'XGBoost' in model_name:
                    # For tree explainers
                    shap_values = explainer.shap_values(X_processed)
                    if isinstance(shap_values, list):
                        # Binary classification - take positive class
                        shap_values = shap_values[1]
                        
                elif 'LightGBM' in model_name:
                    # For LightGBM with potential additivity issues
                    try:
                        # Try normal calculation first
                        shap_values = explainer.shap_values(X_processed)
                        if isinstance(shap_values, list):
                            shap_values = shap_values[1]
                    except Exception as lgb_error:
                        try:
                            # If it's a TreeExplainer, try with check_additivity=False
                            if hasattr(explainer, 'model'):
                                shap_values = explainer.shap_values(X_processed, check_additivity=False)
                                if isinstance(shap_values, list):
                                    shap_values = shap_values[1]
                            else:
                                # For KernelExplainer
                                shap_values = explainer.shap_values(X_processed, nsamples=100)
                                if len(shap_values.shape) == 3:
                                    shap_values = shap_values[:, :, 0]
                        except Exception as lgb_error2:
                            print(f"    ❌ LightGBM SHAP failed: {str(lgb_error2)}")
                            continue
                    
                elif 'Logistic' in model_name:
                    # For linear explainers
                    shap_values = explainer.shap_values(X_processed)
                    
                elif model_name == 'MLP':
                    # For MLP KernelExplainer
                    print(f"    🔄 Computing SHAP values with KernelExplainer (this may take a while)...")
                    shap_values = explainer.shap_values(X_processed, nsamples=100)
                    # MLP KernelExplainer returns 1D array for binary classification
                    if len(shap_values.shape) == 2:
                        # Already in the right shape
                        pass
                    elif len(shap_values.shape) == 3:
                        # If 3D, take the appropriate slice
                        shap_values = shap_values[:, :, 0] if shap_values.shape[2] == 1 else shap_values.squeeze()
                    
                else:
                    # For kernel explainers (like SVM)
                    shap_values = explainer.shap_values(X_processed, nsamples=100)
                    if len(shap_values.shape) == 3:
                        # If 3D, take the positive class
                        shap_values = shap_values[:, :, 1] if shap_values.shape[2] > 1 else shap_values[:, :, 0]
                
                self.shap_values[model_name] = {
                    'values': shap_values,
                    'base_value': explainer.expected_value if hasattr(explainer, 'expected_value') else 0,
                    'data': X_processed,
                    'feature_names': self.pipeline.feature_names if model_name == 'MLP' else self.get_feature_names_after_preprocessing(model),
                    'sample_indices': sample_indices,
                    'y_true': y_sample
                }
                
                print(f"  ✓ SHAP values calculated - Shape: {shap_values.shape}")
                
            except Exception as e:
                print(f"  ❌ Error calculating SHAP values for {model_name}: {str(e)}")
        
        return self
    
    def setup_lime_analysis(self):
        """
        Set up LIME explainers for tabular data.
        """
        if not LIME_AVAILABLE:
            print("\n⚠️ LIME not available. Skipping LIME analysis.")
            return self
            
        print("\n" + "=" * 80)
        print("LIME ANALYSIS SETUP")
        print("=" * 80)
        
        for model_name, model in self.pipeline.models.items():
            print(f"\n🔧 Setting up LIME for {model_name}...")
            
            try:
                # Handle different model structures
                if model_name == 'MLP':
                    # MLP is stored as dict with 'model' and 'scaler' keys
                    if isinstance(model, dict) and 'model' in model and 'scaler' in model:
                        print(f"  🔧 Setting up LIME for PyTorch MLP...")
                        
                        # Create MLP prediction wrapper for LIME
                        def mlp_lime_predict_proba(X_array):
                            """Wrapper function for MLP predictions for LIME"""
                            try:
                                # Convert to DataFrame with all feature names
                                X_df = pd.DataFrame(X_array, columns=self.pipeline.feature_names)
                                
                                # Match MLP training preprocessing: scale only numeric columns
                                X_scaled = X_df.copy()
                                numeric_cols = X_df.select_dtypes(include=[np.number]).columns
                                
                                if len(numeric_cols) > 0:
                                    X_scaled[numeric_cols] = model['scaler'].transform(X_df[numeric_cols])
                                
                                # Convert to tensor and predict (use all features like in training)
                                if TORCH_AVAILABLE:
                                    X_tensor = torch.FloatTensor(X_scaled.values)
                                    model['model'].eval()
                                    with torch.no_grad():
                                        proba_pos = model['model'](X_tensor).cpu().numpy().flatten()
                                    proba_neg = 1 - proba_pos
                                    return np.column_stack([proba_neg, proba_pos])
                                else:
                                    # Fallback
                                    n_samples = X_array.shape[0]
                                    return np.column_stack([np.full(n_samples, 0.5), np.full(n_samples, 0.5)])
                            except Exception as e:
                                print(f"      Error in MLP LIME prediction: {e}")
                                n_samples = X_array.shape[0]
                                return np.column_stack([np.full(n_samples, 0.5), np.full(n_samples, 0.5)])
                        
                        # Get training data with ALL features (like in MLP training)
                        X_train_array = self.pipeline.X_train.values
                        
                        # Create LIME explainer
                        explainer = lime.lime_tabular.LimeTabularExplainer(
                            X_train_array,
                            feature_names=self.pipeline.feature_names,
                            class_names=['Benign', 'Malignant'],
                            mode='classification',
                            discretize_continuous=True
                        )
                        
                        self.lime_explanations[model_name] = {
                            'explainer': explainer,
                            'predict_fn': mlp_lime_predict_proba,
                            'feature_names': self.pipeline.feature_names
                        }
                        
                        print(f"  ✓ LIME explainer initialized for PyTorch MLP (all {len(self.pipeline.feature_names)} features)")
                        continue  # Skip to next model after successful MLP setup
                    else:
                        print(f"  ❌ Invalid MLP structure")
                        continue
                elif not hasattr(model, 'named_steps'):
                    print(f"  ❌ {model_name} does not have pipeline structure")
                    continue
                
                # Get processed training data for pipeline-based models
                X_train_processed = model.named_steps['preprocessor'].transform(self.pipeline.X_train)
                X_train_processed = model.named_steps['preprocessor'].transform(self.pipeline.X_train)
                
                # Create LIME explainer
                explainer = lime.lime_tabular.LimeTabularExplainer(
                    X_train_processed,
                    feature_names=self.get_feature_names_after_preprocessing(model),
                    class_names=['Benign', 'Malignant'],
                    mode='classification',
                    discretize_continuous=True
                )
                
                self.lime_explanations[model_name] = {
                    'explainer': explainer,
                    'feature_names': self.get_feature_names_after_preprocessing(model)
                }
                
                print(f"  ✓ LIME explainer initialized")
                
            except Exception as e:
                print(f"  ❌ Error setting up LIME for {model_name}: {str(e)}")
        
        return self
    
    def generate_global_explanations(self):
        """
        Generate global model explanations using SHAP.
        """
        print("\n" + "=" * 80)
        print("GENERATING GLOBAL EXPLANATIONS")
        print("=" * 80)
        
        # Create figure with subplots for each model
        n_models = len(self.shap_values)
        if n_models == 0:
            print("⚠️ No SHAP values available. Run calculate_shap_values() first.")
            return self
        
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        axes = axes.flatten()
        
        for idx, (model_name, shap_data) in enumerate(self.shap_values.items()):
            if idx >= 4:  # Limit to 4 models for display
                break
                
            print(f"\n📊 Creating global explanation for {model_name}...")
            
            try:
                # SHAP Summary Plot
                plt.subplot(2, 2, idx + 1)
                shap.summary_plot(
                    shap_data['values'], 
                    shap_data['data'],
                    feature_names=shap_data['feature_names'],
                    show=False,
                    max_display=10
                )
                plt.title(f'{model_name} - SHAP Summary')
                
                print(f"  ✓ Global explanation created")
                
            except Exception as e:
                print(f"  ❌ Error creating global explanation for {model_name}: {str(e)}")
        
        plt.tight_layout()
        plt.savefig('model_global_explanations.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Feature importance bar plots
        self.create_feature_importance_plots()
        
        return self
    
    def generate_local_explanations(self, n_samples=5):
        """
        Generate local explanations for specific predictions.
        
        Args:
            n_samples (int): Number of samples to explain locally
        """
        print("\n" + "=" * 80)
        print("GENERATING LOCAL EXPLANATIONS")
        print("=" * 80)
        
        if not self.shap_values:
            print("⚠️ No SHAP values available. Run calculate_shap_values() first.")
            return self
        
        # Select interesting cases for local explanations
        model_name = list(self.shap_values.keys())[0]  # Use first model
        shap_data = self.shap_values[model_name]
        
        # Find cases: True Positives, True Negatives, False Positives, False Negatives
        model = self.pipeline.models[model_name]
        y_pred = model.predict(shap_data['data'])
        y_true = shap_data['y_true']
        
        # Identify different prediction types
        tp_indices = np.where((y_pred == 1) & (y_true == 1))[0]
        tn_indices = np.where((y_pred == 0) & (y_true == 0))[0]
        fp_indices = np.where((y_pred == 1) & (y_true == 0))[0]
        fn_indices = np.where((y_pred == 0) & (y_true == 1))[0]
        
        cases_to_explain = []
        case_labels = []
        
        # Select representative cases
        if len(tp_indices) > 0:
            cases_to_explain.append(tp_indices[0])
            case_labels.append("True Positive")
        if len(tn_indices) > 0:
            cases_to_explain.append(tn_indices[0])
            case_labels.append("True Negative")
        if len(fp_indices) > 0:
            cases_to_explain.append(fp_indices[0])
            case_labels.append("False Positive")
        if len(fn_indices) > 0:
            cases_to_explain.append(fn_indices[0])
            case_labels.append("False Negative")
        
        # Limit to requested number of samples
        cases_to_explain = cases_to_explain[:n_samples]
        case_labels = case_labels[:n_samples]
        
        print(f"📊 Explaining {len(cases_to_explain)} local predictions...")
        
        # Create waterfall plots for each case
        fig, axes = plt.subplots(len(cases_to_explain), 1, figsize=(12, 6*len(cases_to_explain)))
        if len(cases_to_explain) == 1:
            axes = [axes]
        
        for idx, (case_idx, case_label) in enumerate(zip(cases_to_explain, case_labels)):
            print(f"  🔍 Explaining {case_label} (Sample {case_idx})")
            
            try:
                plt.subplot(len(cases_to_explain), 1, idx + 1)
                
                # Create SHAP waterfall plot
                shap.plots.waterfall(
                    shap.Explanation(
                        values=shap_data['values'][case_idx],
                        base_values=shap_data['base_value'],
                        data=shap_data['data'][case_idx],
                        feature_names=shap_data['feature_names']
                    ),
                    show=False
                )
                plt.title(f'{model_name} - {case_label} Explanation')
                
            except Exception as e:
                print(f"    ❌ Error explaining case {case_idx}: {str(e)}")
        
        plt.tight_layout()
        plt.savefig('model_local_explanations.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return self
    
    def create_feature_importance_plots(self):
        """Create and save simplified feature importance plots (two panels)."""
        print("\n📊 Creating feature importance plots...")

        # Aggregate importance from stored results
        records = []
        for model_name, methods in self.feature_importance_results.items():
            for method, df in methods.items():
                if method == 'built_in':
                    for _, r in df.iterrows():
                        records.append({'Model': model_name, 'Feature': r['feature'], 'Importance': r['importance']})
                elif method == 'permutation':
                    for _, r in df.iterrows():
                        records.append({'Model': model_name, 'Feature': r['feature'], 'Importance': r['importance_mean']})
                elif method == 'coefficients':
                    for _, r in df.iterrows():
                        records.append({'Model': model_name, 'Feature': r['feature'], 'Importance': r['abs_coefficient']})

        if not records:
            print("⚠️ No feature importance data available.")
            return self

        importance_df = pd.DataFrame(records)

        # Panel 1: Top average features
        avg_importance = (importance_df.groupby('Feature')['Importance']
                                         .mean()
                                         .sort_values(ascending=False))
        top_features = avg_importance.head(15)

        # Panel 2: Heatmap across models for top 10 features
        top_10 = top_features.head(10).index
        heat_df = (importance_df[importance_df['Feature'].isin(top_10)]
                   .pivot_table(index='Feature', columns='Model', values='Importance', aggfunc='mean'))

        fig, axes = plt.subplots(1, 2, figsize=(18, 7))
        sns.barplot(x=top_features.values, y=top_features.index, ax=axes[0])
        axes[0].set_title('Top 15 Features (Avg Importance)')
        axes[0].set_xlabel('Average Importance')
        axes[0].set_ylabel('Feature')

        sns.heatmap(heat_df, annot=True, fmt='.3f', cmap='viridis', ax=axes[1])
        axes[1].set_title('Top 10 Feature Importance by Model')
        axes[1].set_xlabel('Model')
        axes[1].set_ylabel('Feature')

        plt.tight_layout()
        plt.savefig('feature_importance_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("✓ Saved feature_importance_analysis.png (2 panels)")
        return self
    
    def create_clinical_interpretation_report(self):
        """
        Create a clinical interpretation report of the model explanations.
        """
        print("\n" + "=" * 80)
        print("GENERATING CLINICAL INTERPRETATION REPORT")
        print("=" * 80)
        
        report = []
        report.append("# Ovarian Mass Classification - Model Explainability Report\n")
        report.append(f"Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append("=" * 80 + "\n")
        
        # Executive Summary
        report.append("## Executive Summary\n")
        report.append("This report provides a comprehensive analysis of model explainability for the ")
        report.append("ovarian mass classification system. The analysis includes feature importance, ")
        report.append("SHAP values, and clinical interpretations of the machine learning models.\n\n")
        
        # Key Findings from Feature Importance
        report.append("## Key Findings\n")
        
        if self.feature_importance_results:
            # Find most consistently important features
            all_features = {}
            for model_name, results in self.feature_importance_results.items():
                for method, df in results.items():
                    if method in ['built_in', 'permutation']:
                        for _, row in df.head(10).iterrows():  # Top 10 from each
                            feature = row['feature']
                            if feature not in all_features:
                                all_features[feature] = 0
                            all_features[feature] += 1
            
            # Sort by consistency across models
            consistent_features = sorted(all_features.items(), key=lambda x: x[1], reverse=True)
            
            report.append("### Most Consistently Important Features:\n")
            for feature, count in consistent_features[:10]:
                report.append(f"- **{feature}**: Identified as important in {count} model-method combinations\n")
            
            report.append("\n### Clinical Interpretation:\n")
            
            # Clinical interpretation of top features
            clinical_interpretations = {
                'CA125 (U/mL)': "CA125 is a well-established tumor marker for ovarian cancer. Higher levels typically indicate malignancy.",
                'HE4 (pmol/L)': "HE4 is another important biomarker that complements CA125 in ovarian cancer detection.",
                'AGE': "Age is a significant risk factor, with ovarian cancer incidence increasing with age.",
                'CEA': "CEA can be elevated in various cancers and may help in differential diagnosis.",
                'Menopausal Status': "Post-menopausal status is associated with higher ovarian cancer risk.",
                'Type of Mass': "The morphological characteristics of the mass are crucial for classification.",
                'USG': "Ultrasound findings provide important structural information about the mass.",
                'CT Scan': "CT imaging offers detailed anatomical information for classification."
            }
            
            for feature, count in consistent_features[:5]:
                if feature in clinical_interpretations:
                    report.append(f"- **{feature}**: {clinical_interpretations[feature]}\n")
            report.append("\n")
        
        # Model-Specific Insights
        report.append("## Model-Specific Insights\n")
        
        for model_name in self.pipeline.models.keys():
            report.append(f"### {model_name}\n")
            
            # Performance metrics
            if model_name in self.pipeline.results:
                results = self.pipeline.results[model_name]
                report.append(f"- **Validation AUC**: {results.get('val_auc', 'N/A'):.4f}\n")
                report.append(f"- **Validation F1**: {results.get('val_f1', 'N/A'):.4f}\n")
                report.append(f"- **Validation Precision**: {results.get('val_precision', 'N/A'):.4f}\n")
                report.append(f"- **Validation Recall**: {results.get('val_recall', 'N/A'):.4f}\n")
            
            # Feature importance insights
            if model_name in self.feature_importance_results:
                results = self.feature_importance_results[model_name]
                
                for method, df in results.items():
                    report.append(f"\n**Top features by {method}**:\n")
                    for idx, (_, row) in enumerate(df.head(5).iterrows()):
                        if method == 'coefficients':
                            report.append(f"{idx+1}. {row['feature']}: {row['coefficient']:.4f}\n")
                        else:
                            report.append(f"{idx+1}. {row['feature']}: {row.get('importance', row.get('importance_mean', 0)):.4f}\n")
            
            report.append("\n")
        
        # Recommendations
        report.append("## Clinical Recommendations\n")
        report.append("Based on the explainability analysis, the following recommendations are made:\n\n")
        report.append("1. **Primary Biomarkers**: Focus on CA125 and HE4 levels as they consistently ")
        report.append("show high importance across models.\n")
        report.append("2. **Patient Demographics**: Age and menopausal status are important factors ")
        report.append("and should be carefully considered in clinical assessment.\n")
        report.append("3. **Imaging Features**: Ultrasound and CT scan findings provide crucial ")
        report.append("morphological information for accurate classification.\n")
        report.append("4. **Model Selection**: Different models emphasize different features, suggesting ")
        report.append("an ensemble approach may capture complementary information.\n")
        report.append("5. **Feature Monitoring**: Monitor the most important features for model drift ")
        report.append("and performance changes over time.\n\n")
        
        # Limitations
        report.append("## Limitations and Considerations\n")
        report.append("- The explainability analysis is based on the current dataset and may not ")
        report.append("generalize to all populations.\n")
        report.append("- SHAP values represent feature contributions but should be interpreted ")
        report.append("alongside clinical expertise.\n")
        report.append("- Feature interactions may not be fully captured in individual feature importance scores.\n")
        report.append("- Regular revalidation of feature importance is recommended as new data becomes available.\n")
        
        # Save the report
        report_content = "".join(report)
        
        with open('model_explainability_report.md', 'w') as f:
            f.write(report_content)
        
        print("✅ Clinical interpretation report saved as 'model_explainability_report.md'")
        print("\n" + "=" * 50)
        print("REPORT PREVIEW:")
        print("=" * 50)
        print(report_content[:1000] + "..." if len(report_content) > 1000 else report_content)
        
        return self
    
    def get_feature_names_after_preprocessing(self, model):
        """
        Get feature names after preprocessing transformation.
        
        Args:
            model: Trained sklearn pipeline or custom model structure
            
        Returns:
            list: Feature names after preprocessing
        """
        try:
            # Handle different model structures
            if not hasattr(model, 'named_steps'):
                # For custom structures (like MLP), use original feature names
                return self.pipeline.feature_names
            
            # Try to get feature names from the preprocessor
            if hasattr(model.named_steps['preprocessor'], 'get_feature_names_out'):
                return model.named_steps['preprocessor'].get_feature_names_out()
            else:
                # Fallback: use original feature names
                return self.pipeline.feature_names
        except:
            # Ultimate fallback: create generic names
            try:
                if hasattr(model, 'named_steps'):
                    n_features = model.named_steps['preprocessor'].transform(self.pipeline.X_train).shape[1]
                else:
                    n_features = len(self.pipeline.feature_names)
                return [f'feature_{i}' for i in range(n_features)]
            except:
                # Last resort fallback
                return self.pipeline.feature_names
    
    def run_complete_explainability_analysis(self):
        """
        Run the complete explainability analysis pipeline.
        """
        try:
            print("\n🚀 STARTING COMPREHENSIVE EXPLAINABILITY ANALYSIS")
            print("=" * 80)
            
            # Setup pipeline if needed
            self.setup_pipeline()
            
            # Run all analysis steps
            self.analyze_feature_importance()
            self.setup_shap_analysis()
            self.calculate_shap_values(sample_size=100)
            self.setup_lime_analysis()
            self.generate_global_explanations()
            self.generate_local_explanations(n_samples=4)
            self.create_clinical_interpretation_report()
            
            print("\n" + "=" * 80)
            print("🎉 EXPLAINABILITY ANALYSIS COMPLETED SUCCESSFULLY!")
            print("=" * 80)
            
            # Summary of generated files
            print(f"\n📁 Generated Files:")
            print(f"  - model_global_explanations.png")
            print(f"  - model_local_explanations.png")
            print(f"  - feature_importance_analysis.png")
            print(f"  - model_explainability_report.md")
            
            return self
            
        except Exception as e:
            print(f"❌ Explainability analysis failed with error: {str(e)}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """
    Main execution function for explainability analysis.
    """
    print("Ovarian Mass Classification - Explainability Analysis")
    print("=" * 60)
    
    # Run the explainability analysis
    explainer = ModelExplainabilityAnalysis()
    result = explainer.run_complete_explainability_analysis()
    
    if result is not None:
        print("\n✅ Explainability analysis completed successfully!")
        print("\nThe analysis provides insights into:")
        print("  🔍 Feature importance across all models")
        print("  📊 SHAP value explanations")
        print("  🏥 Clinical interpretation of model decisions")
        print("  📈 Global and local model explanations")
        print("  📋 Comprehensive explainability report")
    else:
        print("\n❌ Explainability analysis failed!")


if __name__ == "__main__":
    main()
