<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ovarian Cancer Prediction Tool - AIIMS Bhubaneswar</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1 class="header-title">Ovarian Cancer Prediction Tool</h1>
            <p class="header-subtitle">AIIMS Bhubaneswar - AI-Powered Medical Decision Support</p>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <div class="app-grid">
                <!-- Input Form Section -->
                <section class="input-section">
                    <div class="section-header">
                        <h2>Patient Information & Medical Data</h2>
                        <p>Enter patient details for ovarian cancer risk assessment</p>
                    </div>

                    <form id="predictionForm" class="prediction-form">
                        <!-- Demographics -->
                        <div class="form-category">
                            <div class="category-header" onclick="toggleCategory('demographic')">
                                <h3>👤 Demographics</h3>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div id="demographic" class="category-content">
                                <div class="form-group">
                                    <label class="form-label">Age (years)</label>
                                    <input type="number" id="AGE" class="form-control" min="15" max="80" step="1" value="42" required>
                                    <small class="help-text">Patient age in years</small>
                                </div>
                            </div>
                        </div>

                        <!-- Gynecological History -->
                        <div class="form-category">
                            <div class="category-header" onclick="toggleCategory('gynecological')">
                                <h3>🩺 Gynecological History</h3>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div id="gynecological" class="category-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Parity</label>
                                        <select id="Parity" class="form-control" required>
                                            <option value="0">Nulliparous</option>
                                            <option value="1">Para 1</option>
                                            <option value="2">Para 2</option>
                                            <option value="3" selected>Para 3+</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Age of Menarche</label>
                                        <select id="Age of Menarche" class="form-control" required>
                                            <option value="0">&lt;12 years</option>
                                            <option value="1">12-13 years</option>
                                            <option value="2" selected>13-14 years</option>
                                            <option value="3">&gt;14 years</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Menopausal Status</label>
                                    <select id="Menopausal Status" class="form-control" required>
                                        <option value="1" selected>Pre-menopausal</option>
                                        <option value="2">Post-menopausal</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Medical History -->
                        <div class="form-category">
                            <div class="category-header" onclick="toggleCategory('medical_history')">
                                <h3>📋 Medical History</h3>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div id="medical_history" class="category-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">History of OCP Use</label>
                                        <select id="H/O OCP" class="form-control" required>
                                            <option value="1">Yes</option>
                                            <option value="2" selected>No</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">History of Miscarriage</label>
                                        <select id="Past H/O Miscarriage" class="form-control" required>
                                            <option value="0">Unknown</option>
                                            <option value="1">Yes</option>
                                            <option value="2" selected>No</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">History of PCOS</label>
                                        <select id="Past H/O PCOS" class="form-control" required>
                                            <option value="0">Unknown</option>
                                            <option value="1">Yes</option>
                                            <option value="2" selected>No</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">History of Endometriosis</label>
                                        <select id="Past H/O Endometriosis" class="form-control" required>
                                            <option value="1">Yes</option>
                                            <option value="2" selected>No</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Hormone Replacement Therapy</label>
                                        <select id="Under Hormone Replacement Therapy" class="form-control" required>
                                            <option value="1">Yes</option>
                                            <option value="2" selected>No</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Tubal Ligation</label>
                                        <select id="Undergone Tubal Ligation" class="form-control" required>
                                            <option value="1">Yes</option>
                                            <option value="2" selected>No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Imaging Findings -->
                        <div class="form-category">
                            <div class="category-header" onclick="toggleCategory('imaging')">
                                <h3>🖼️ Imaging Findings</h3>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div id="imaging" class="category-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Ultrasound Findings</label>
                                        <select id="USG" class="form-control" required>
                                            <option value="1" selected>Benign pattern</option>
                                            <option value="3">Suspicious pattern</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">CT Scan Findings</label>
                                        <select id="CT Scan" class="form-control" required>
                                            <option value="1" selected>Benign pattern</option>
                                            <option value="3">Suspicious pattern</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Type of Mass</label>
                                    <select id="Type of Mass" class="form-control" required>
                                        <option value="1" selected>Simple/Cystic</option>
                                        <option value="2">Complex/Solid</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Tumor Markers -->
                        <div class="form-category">
                            <div class="category-header" onclick="toggleCategory('biomarkers')">
                                <h3>🧬 Tumor Markers</h3>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div id="biomarkers" class="category-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">HE4 (pmol/L)</label>
                                        <input type="number" id="HE4 (pmol/L)" class="form-control" min="0" max="5000" step="1" value="67" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">CA125 (U/mL)</label>
                                        <input type="number" id="CA125 (U/mL)" class="form-control" min="0" max="8000" step="0.1" value="59" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">CEA (ng/mL)</label>
                                        <input type="number" id="CEA" class="form-control" min="0" max="250" step="0.01" value="4.36" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">FSH (mIU/mL)</label>
                                        <input type="number" id="FSH (mIU/mL)" class="form-control" min="0" max="200" step="1" value="46" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Apolipoprotein A1 (mg/dL)</label>
                                        <input type="number" id="Apo A1 (mg/dL)" class="form-control" min="0" max="300" step="1" value="112" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Transferrin (mg/dL)</label>
                                        <input type="number" id="Transferrin (mg/dL)" class="form-control" min="0" max="600" step="1" value="178" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Laboratory Tests -->
                        <div class="form-category">
                            <div class="category-header" onclick="toggleCategory('laboratory')">
                                <h3>🔬 Laboratory Tests</h3>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div id="laboratory" class="category-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Fasting Blood Sugar (mg/dL)</label>
                                        <input type="number" id="FBS (mg/dL)" class="form-control" min="50" max="250" step="0.1" value="104" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Urea (mg/dL)</label>
                                        <input type="number" id="Urea (mg/dL)" class="form-control" min="5" max="150" step="1" value="25" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Creatinine (mg/dL)</label>
                                        <input type="number" id="Creatinine (mg/dL)" class="form-control" min="0.3" max="2.0" step="0.1" value="0.9" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Prothrombin Time (seconds)</label>
                                        <input type="number" id="Prothrombin time" class="form-control" min="8" max="25" step="0.1" value="12.05" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Lipid Profile -->
                        <div class="form-category">
                            <div class="category-header" onclick="toggleCategory('lipid_profile')">
                                <h3>🫀 Lipid Profile</h3>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div id="lipid_profile" class="category-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Total Cholesterol (mg/dL)</label>
                                        <input type="number" id="TC" class="form-control" min="100" max="350" step="1" value="204" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">HDL Cholesterol (mg/dL)</label>
                                        <input type="number" id="HDLc" class="form-control" min="20" max="80" step="1" value="47" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">LDL Cholesterol (mg/dL)</label>
                                        <input type="number" id="LDLc" class="form-control" min="50" max="200" step="0.1" value="112" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Triglycerides (mg/dL)</label>
                                        <input type="number" id="TG (mg/dL)" class="form-control" min="50" max="600" step="1" value="165" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="form-actions">
                            <button type="button" class="btn btn--primary btn--lg" id="predictBtn">
                                <span class="btn-text">Predict Diagnosis</span>
                                <span class="btn-loading hidden">Analyzing...</span>
                            </button>
                            <button type="button" class="btn btn--secondary btn--lg" id="clearBtn">Clear Fields</button>
                            <button type="button" class="btn btn--outline btn--lg" id="loadExampleBtn">Load Example Case</button>
                        </div>
                    </form>
                </section>

                <!-- Results Section -->
                <section class="results-section">
                    <div class="section-header">
                        <h2>Prediction Results</h2>
                        <p>AI-powered ovarian cancer risk assessment</p>
                    </div>

                    <div id="resultsContainer" class="results-container hidden">
                        <!-- Risk Assessment Card -->
                        <div class="card result-card">
                            <div class="card__header">
                                <h3>Risk Assessment</h3>
                            </div>
                            <div class="card__body">
                                <div class="risk-display">
                                    <div class="risk-gauge-container">
                                        <canvas id="riskGauge" width="200" height="200"></canvas>
                                    </div>
                                    <div class="risk-details">
                                        <div class="prediction-result">
                                            <h4 id="predictionLabel">Prediction</h4>
                                            <p id="predictionConfidence">Confidence</p>
                                        </div>
                                        <div class="probability-bars">
                                            <div class="prob-bar">
                                                <span>Benign</span>
                                                <div class="prob-bar-bg">
                                                    <div class="prob-bar-fill benign" id="benignProb"></div>
                                                </div>
                                                <span id="benignPercent">0%</span>
                                            </div>
                                            <div class="prob-bar">
                                                <span>Malignant</span>
                                                <div class="prob-bar-bg">
                                                    <div class="prob-bar-fill malignant" id="malignantProb"></div>
                                                </div>
                                                <span id="malignantPercent">0%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Clinical Interpretation -->
                        <div class="card interpretation-card">
                            <div class="card__header">
                                <h3>Clinical Interpretation</h3>
                            </div>
                            <div class="card__body">
                                <div id="clinicalInterpretation">
                                    <h4 id="interpretationTitle"></h4>
                                    <p id="interpretationDescription"></p>
                                    <div class="recommendations">
                                        <h5>Recommendations:</h5>
                                        <ul id="recommendationsList"></ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Visualizations -->
                        <div class="charts-grid">
                            <div class="card chart-card">
                                <div class="card__header">
                                    <h3>Feature Importance</h3>
                                </div>
                                <div class="card__body">
                                    <div class="chart-container" style="position: relative; height: 300px;">
                                        <canvas id="featureImportanceChart"></canvas>
                                    </div>
                                </div>
                            </div>

                            <div class="card chart-card">
                                <div class="card__header">
                                    <h3>Biomarker Profile</h3>
                                </div>
                                <div class="card__body">
                                    <div class="chart-container" style="position: relative; height: 300px;">
                                        <canvas id="biomarkerChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Export Actions -->
                        <div class="export-actions">
                            <button type="button" class="btn btn--primary" onclick="exportToPDF()">📄 Export PDF Report</button>
                        </div>
                    </div>

                    <!-- Model Performance Info -->
                    <div class="card model-info-card">
                        <div class="card__header">
                            <h3>Model Performance</h3>
                        </div>
                        <div class="card__body">
                            <div class="performance-metrics">
                                <div class="metric">
                                    <span class="metric-label">Best Model</span>
                                    <span class="metric-value">SVM</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">AUC Score</span>
                                    <span class="metric-value">0.9881</span>
                                </div>
                            </div>
                            <p class="model-disclaimer">
                                <strong>Disclaimer:</strong> This is a medical decision support tool and should not replace clinical judgment. Always consult with qualified healthcare professionals for medical decisions.
                            </p>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <script src="app.js"></script>
</body>
</html>