// Medical Application JavaScript

// Application State
let currentPrediction = null;
let featureImportanceChart = null;
let biomarkerChart = null;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadDefaultValues();
});

function initializeApp() {
    // Initialize all categories as expanded
    const categories = ['demographic', 'gynecological', 'medical_history', 'imaging', 'biomarkers', 'laboratory', 'lipid_profile'];
    categories.forEach(category => {
        const element = document.getElementById(category);
        if (element) {
            element.classList.remove('collapsed');
        }
    });
    
    console.log('Ovarian Cancer Prediction Tool initialized');
}

function loadDefaultValues() {
    const defaults = {
        'AGE': 42,
        'USG': 1,
        'CT Scan': 1,
        'Type of Mass': 1,
        'Parity': 3,
        'Age of Menarche': 2,
        'H/O OCP': 2,
        'Past H/O Miscarriage': 2,
        'Past H/O PCOS': 2,
        'Past H/O Endometriosis': 2,
        'Menopausal Status': 1,
        'Under Hormone Replacement Therapy': 2,
        'Undergone Tubal Ligation': 2,
        'HE4 (pmol/L)': 67,
        'FSH (mIU/mL)': 46,
        'Apo A1 (mg/dL)': 112,
        'Transferrin (mg/dL)': 178,
        'CA125 (U/mL)': 59,
        'FBS (mg/dL)': 104,
        'Urea (mg/dL)': 25,
        'Creatinine (mg/dL)': 0.9,
        'TC': 204,
        'HDLc': 47,
        'LDLc': 112,
        'TG (mg/dL)': 165,
        'CEA': 4.36,
        'Prothrombin time': 12.05
    };
    
    Object.keys(defaults).forEach(fieldId => {
        const input = document.getElementById(fieldId);
        if (input) {
            input.value = defaults[fieldId];
        }
    });
}

function setupEventListeners() {
    const form = document.getElementById('predictionForm');
    if (form) {
        form.addEventListener('submit', handlePrediction);
    }
    
    // Setup input validation with less restrictive approach
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            clearFieldError(input);
        });
        input.addEventListener('blur', function() {
            validateField(input);
        });
    });
}

// Category Toggle Functionality
function toggleCategory(categoryId) {
    const content = document.getElementById(categoryId);
    const header = content.previousElementSibling;
    const icon = header.querySelector('.toggle-icon');
    
    if (content.classList.contains('collapsed')) {
        content.classList.remove('collapsed');
        header.classList.remove('collapsed');
        icon.textContent = '▼';
    } else {
        content.classList.add('collapsed');
        header.classList.add('collapsed');
        icon.textContent = '▶';
    }
}

// Form Validation - Fixed to be less restrictive
function validateField(field) {
    if (!field.value) return true; // Allow empty fields
    
    if (field.type === 'number') {
        const value = parseFloat(field.value);
        const min = parseFloat(field.min);
        const max = parseFloat(field.max);
        
        if (isNaN(value)) {
            showFieldError(field, 'Please enter a valid number');
            return false;
        }
        
        if (min && value < min) {
            showFieldError(field, `Value must be at least ${min}`);
            return false;
        }
        
        if (max && value > max) {
            showFieldError(field, `Value must not exceed ${max}`);
            return false;
        }
    }
    
    clearFieldError(field);
    return true;
}

function showFieldError(field, message) {
    clearFieldError(field);
    field.classList.add('error');
    const errorElement = document.createElement('small');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    errorElement.style.color = '#ef4444';
    errorElement.style.fontSize = '12px';
    errorElement.style.display = 'block';
    errorElement.style.marginTop = '4px';
    field.parentNode.appendChild(errorElement);
}

function clearFieldError(field) {
    field.classList.remove('error');
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
}

// Feature Engineering Functions
function calculateDerivedFeatures(data) {
    const features = {...data};
    
    // Calculate interaction features
    features.CA125_HE4_interaction = data['CA125 (U/mL)'] * data['HE4 (pmol/L)'];
    features.AGE_CA125_interaction = data.AGE * data['CA125 (U/mL)'];
    features.AGE_HE4_interaction = data.AGE * data['HE4 (pmol/L)'];
    
    // ROMA-like score
    features.ROMA_like_score = Math.log(data['CA125 (U/mL)'] + 1) + 
                              Math.log(data['HE4 (pmol/L)'] + 1) + 
                              data['Menopausal Status'];
    
    // Ratio features
    features.TC_HDL_ratio = data.TC / data.HDLc;
    features.LDL_HDL_ratio = data.LDLc / data.HDLc;
    features.CA125_CEA_ratio = data['CA125 (U/mL)'] / data.CEA;
    
    // Age groups (binned)
    if (data.AGE <= 30) features.Age_group = 0;
    else if (data.AGE <= 40) features.Age_group = 1;
    else if (data.AGE <= 50) features.Age_group = 2;
    else if (data.AGE <= 60) features.Age_group = 3;
    else features.Age_group = 4;
    
    // CA125 risk categories
    if (data['CA125 (U/mL)'] <= 35) features.CA125_risk_category = 0;
    else if (data['CA125 (U/mL)'] <= 200) features.CA125_risk_category = 1;
    else if (data['CA125 (U/mL)'] <= 1000) features.CA125_risk_category = 2;
    else features.CA125_risk_category = 3;
    
    return features;
}

// Simulated ML Prediction
function simulateSVMPrediction(features) {
    let riskScore = 0;
    
    // Major risk factors based on clinical knowledge
    const ca125 = features['CA125 (U/mL)'];
    const he4 = features['HE4 (pmol/L)'];
    const age = features.AGE;
    const usg = features.USG;
    const ctScan = features['CT Scan'];
    const massType = features['Type of Mass'];
    const menopausalStatus = features['Menopausal Status'];
    
    // CA125 contribution (highest weight)
    if (ca125 > 200) riskScore += 0.4;
    else if (ca125 > 100) riskScore += 0.25;
    else if (ca125 > 35) riskScore += 0.1;
    
    // HE4 contribution (second highest weight)
    if (he4 > 150) riskScore += 0.3;
    else if (he4 > 100) riskScore += 0.2;
    else if (he4 > 70) riskScore += 0.1;
    
    // Age factor
    if (age > 50) riskScore += 0.15;
    else if (age > 40) riskScore += 0.08;
    
    // Imaging findings
    if (usg === 3) riskScore += 0.2; // Suspicious pattern
    if (ctScan === 3) riskScore += 0.2; // Suspicious pattern
    if (massType === 2) riskScore += 0.15; // Complex/Solid
    
    // Menopausal status
    if (menopausalStatus === 2) riskScore += 0.1; // Post-menopausal
    
    // ROMA-like score influence
    if (features.ROMA_like_score > 8) riskScore += 0.2;
    else if (features.ROMA_like_score > 6) riskScore += 0.1;
    
    // CEA contribution
    if (features.CEA > 10) riskScore += 0.1;
    
    // Interaction effects
    if (features.CA125_HE4_interaction > 10000) riskScore += 0.15;
    if (features.AGE_CA125_interaction > 3000) riskScore += 0.1;
    
    // Apply some randomness to make it more realistic (-0.05 to +0.05)
    riskScore += (Math.random() - 0.5) * 0.1;
    
    // Ensure score is between 0 and 1
    riskScore = Math.max(0, Math.min(1, riskScore));
    
    // Convert to probability (sigmoid-like function)
    const malignantProb = 1 / (1 + Math.exp(-5 * (riskScore - 0.5)));
    const benignProb = 1 - malignantProb;
    
    return {
        benignProb: benignProb,
        malignantProb: malignantProb,
        prediction: malignantProb > 0.5 ? 'Malignant' : 'Benign',
        confidence: Math.max(benignProb, malignantProb),
        riskScore: riskScore
    };
}

// Handle Prediction
async function handlePrediction(event) {
    event.preventDefault();
    
    const btn = document.getElementById('predictBtn');
    const resultsContainer = document.getElementById('resultsContainer');
    
    // Show loading state
    btn.classList.add('loading');
    btn.disabled = true;
    
    try {
        // Collect form data
        const formData = collectFormData();
        
        // Validate required fields only
        if (!validateRequiredFields()) {
            throw new Error('Please fill in all required fields');
        }
        
        // Calculate derived features
        const features = calculateDerivedFeatures(formData);
        
        // Simulate prediction delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Get prediction
        const prediction = simulateSVMPrediction(features);
        
        // Store current prediction
        currentPrediction = {
            inputs: formData,
            features: features,
            prediction: prediction,
            timestamp: new Date()
        };
        
        // Display results
        displayResults(prediction);
        
        // Show results container with animation
        resultsContainer.classList.remove('hidden');
        resultsContainer.classList.add('fade-in');
        
        // Scroll to results
        setTimeout(() => {
            resultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 200);
        
    } catch (error) {
        console.error('Prediction error:', error);
        alert('Error: ' + error.message);
    } finally {
        // Reset button state
        btn.classList.remove('loading');
        btn.disabled = false;
    }
}

function collectFormData() {
    const data = {};
    const inputs = document.querySelectorAll('.form-control');
    
    inputs.forEach(input => {
        let value;
        if (input.type === 'number') {
            value = parseFloat(input.value) || 0;
        } else {
            value = parseInt(input.value) || 0;
        }
        data[input.id] = value;
    });
    
    return data;
}

function validateRequiredFields() {
    let isValid = true;
    const inputs = document.querySelectorAll('.form-control[required]');
    
    inputs.forEach(input => {
        if (!input.value || input.value.trim() === '') {
            showFieldError(input, 'This field is required');
            isValid = false;
        } else if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Display Results
function displayResults(prediction) {
    // Update prediction labels
    document.getElementById('predictionLabel').textContent = prediction.prediction;
    document.getElementById('predictionConfidence').textContent = 
        `Confidence: ${(prediction.confidence * 100).toFixed(1)}%`;
    
    // Update probability bars
    const benignPercent = (prediction.benignProb * 100).toFixed(1);
    const malignantPercent = (prediction.malignantProb * 100).toFixed(1);
    
    document.getElementById('benignProb').style.width = benignPercent + '%';
    document.getElementById('malignantProb').style.width = malignantPercent + '%';
    document.getElementById('benignPercent').textContent = benignPercent + '%';
    document.getElementById('malignantPercent').textContent = malignantPercent + '%';
    
    // Update clinical interpretation
    updateClinicalInterpretation(prediction);
    
    // Create visualizations
    createRiskGauge(prediction.malignantProb);
    createFeatureImportanceChart();
    createBiomarkerChart();
}

function updateClinicalInterpretation(prediction) {
    const interpretationCard = document.querySelector('.interpretation-card');
    const title = document.getElementById('interpretationTitle');
    const description = document.getElementById('interpretationDescription');
    const recommendationsList = document.getElementById('recommendationsList');
    
    if (prediction.prediction === 'Malignant') {
        interpretationCard.classList.add('malignant');
        title.textContent = 'Malignant Ovarian Mass';
        description.textContent = 'The AI model indicates a potentially cancerous ovarian growth that requires immediate medical attention and further evaluation.';
        
        const malignantRecommendations = [
            'Urgent referral to gynecologic oncologist',
            'Complete staging workup including imaging',
            'Multidisciplinary team consultation',
            'Consider neoadjuvant chemotherapy if advanced stage',
            'Genetic counseling for hereditary cancer syndromes'
        ];
        
        recommendationsList.innerHTML = malignantRecommendations
            .map(rec => `<li>${rec}</li>`).join('');
            
    } else {
        interpretationCard.classList.remove('malignant');
        title.textContent = 'Benign Ovarian Mass';
        description.textContent = 'The AI model suggests a non-cancerous ovarian growth with good prognosis, though continued monitoring is recommended.';
        
        const benignRecommendations = [
            'Regular follow-up with gynecologist',
            'Periodic imaging to monitor size changes',
            'Consider laparoscopic removal if symptomatic',
            'Monitor tumor markers if elevated',
            'Patient education about symptoms to watch for'
        ];
        
        recommendationsList.innerHTML = benignRecommendations
            .map(rec => `<li>${rec}</li>`).join('');
    }
}

// Chart Creation Functions
function createRiskGauge(riskLevel) {
    const canvas = document.getElementById('riskGauge');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 80;
    
    // Background arc
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0.75 * Math.PI, 0.25 * Math.PI);
    ctx.strokeStyle = '#334155';
    ctx.lineWidth = 20;
    ctx.stroke();
    
    // Risk level arc
    const endAngle = 0.75 * Math.PI + (1.5 * Math.PI * riskLevel);
    const color = riskLevel > 0.5 ? '#ef4444' : '#10b981';
    
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0.75 * Math.PI, endAngle);
    ctx.strokeStyle = color;
    ctx.lineWidth = 20;
    ctx.stroke();
    
    // Center text
    ctx.fillStyle = '#000000';
    ctx.font = 'bold 24px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(`${(riskLevel * 100).toFixed(0)}%`, centerX, centerY - 10);
    
    ctx.font = '14px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
    ctx.fillText('Risk Level', centerX, centerY + 15);
}

function createFeatureImportanceChart() {
    const canvas = document.getElementById('featureImportanceChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    if (featureImportanceChart) {
        featureImportanceChart.destroy();
    }
    
    // Simulated feature importance based on clinical knowledge
    const features = [
        'CA125 (U/mL)',
        'HE4 (pmol/L)',
        'Imaging Findings',
        'Age',
        'CA125-HE4 Interaction',
        'Type of Mass',
        'ROMA Score',
        'Menopausal Status',
        'CEA',
        'Age-CA125 Interaction'
    ];
    
    const importance = [0.85, 0.72, 0.68, 0.45, 0.42, 0.38, 0.35, 0.28, 0.22, 0.18];
    
    featureImportanceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: features,
            datasets: [{
                label: 'Feature Importance',
                data: importance,
                backgroundColor: ['#1FB8CD', '#FFC185', '#B4413C', '#ECEBD5', '#5D878F', '#DB4545', '#D2BA4C', '#964325', '#944454', '#13343B'],
                borderColor: '#3b82f6',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: { display: false },
                title: {
                    display: true,
                    text: 'Top 10 Most Important Features',
                    color: '#111827'
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    max: 1,
                    ticks: { color: '#111827' },
                    grid: { color: '#e5e7eb' }
                },
                y: {
                    ticks: { 
                        color: '#111827',
                        font: { size: 10 }
                    },
                    grid: { color: '#e5e7eb' }
                }
            }
        }
    });
}

function createBiomarkerChart() {
    const canvas = document.getElementById('biomarkerChart');
    if (!canvas || !currentPrediction) return;
    
    const ctx = canvas.getContext('2d');
    
    if (biomarkerChart) {
        biomarkerChart.destroy();
    }
    
    const data = currentPrediction.inputs;
    
    // Normalize biomarker values to 0-100 scale for radar chart
    const normalizeValue = (value, max) => Math.min(100, (value / max) * 100);
    
    biomarkerChart = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['CA125', 'HE4', 'CEA', 'FSH', 'Apo A1', 'Transferrin'],
            datasets: [{
                label: 'Patient Values',
                data: [
                    normalizeValue(data['CA125 (U/mL)'], 200),
                    normalizeValue(data['HE4 (pmol/L)'], 150),
                    normalizeValue(data.CEA, 20),
                    normalizeValue(data['FSH (mIU/mL)'], 100),
                    normalizeValue(data['Apo A1 (mg/dL)'], 200),
                    normalizeValue(data['Transferrin (mg/dL)'], 400)
                ],
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderColor: '#3b82f6',
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointHoverBackgroundColor: '#ffffff',
                pointHoverBorderColor: '#3b82f6'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { 
                    display: true,
                    labels: { color: '#111827' }
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: { 
                        color: '#111827',
                        backdropColor: '#ffffff'
                    },
                    grid: { color: '#e5e7eb' },
                    pointLabels: { color: '#111827' }
                }
            }
        }
    });
}

// Utility Functions - FIXED
function clearFields() {
    const inputs = document.querySelectorAll('.form-control');
    
    // Clear all field values and errors
    inputs.forEach(input => {
        input.value = '';
        clearFieldError(input);
    });
    
    // Then reload default values
    setTimeout(() => {
        loadDefaultValues();
    }, 100);
    
    // Hide results
    const resultsContainer = document.getElementById('resultsContainer');
    if (resultsContainer) {
        resultsContainer.classList.add('hidden');
    }
    
    currentPrediction = null;
    
    // Show confirmation
    alert('All fields have been reset to default values');
}

function loadExampleCase() {
    // Randomly load either a malignant (high-risk) or benign (low-risk) example case
    const malignantCase = {
        'AGE': 65,
        'USG': 3, // Suspicious
        'CT Scan': 3, // Suspicious
        'Type of Mass': 2, // Complex/Solid
        'Parity': 0, // Nulliparous
        'Age of Menarche': 1, // Early menarche
        'H/O OCP': 2, // No OCP
        'Past H/O Miscarriage': 2,
        'Past H/O PCOS': 2,
        'Past H/O Endometriosis': 2,
        'Menopausal Status': 2, // Post-menopausal
        'Under Hormone Replacement Therapy': 2,
        'Undergone Tubal Ligation': 2,
        'HE4 (pmol/L)': 250, // Very elevated
        'FSH (mIU/mL)': 95,
        'Apo A1 (mg/dL)': 85,
        'Transferrin (mg/dL)': 145,
        'CA125 (U/mL)': 420, // Very elevated
        'FBS (mg/dL)': 125,
        'Urea (mg/dL)': 35,
        'Creatinine (mg/dL)': 1.3,
        'TC': 245,
        'HDLc': 35,
        'LDLc': 145,
        'TG (mg/dL)': 220,
        'CEA': 15.8, // Very elevated
        'Prothrombin time': 14.2
    };
    
    const benignCase = {
        'AGE': 28,
        'USG': 1, // Benign
        'CT Scan': 1, // Benign
        'Type of Mass': 1, // Simple/Cystic
        'Parity': 2,
        'Age of Menarche': 2,
        'H/O OCP': 1, // Yes (protective)
        'Past H/O Miscarriage': 2,
        'Past H/O PCOS': 2,
        'Past H/O Endometriosis': 2,
        'Menopausal Status': 1, // Pre-menopausal
        'Under Hormone Replacement Therapy': 2,
        'Undergone Tubal Ligation': 2,
        'HE4 (pmol/L)': 45,
        'FSH (mIU/mL)': 8,
        'Apo A1 (mg/dL)': 155,
        'Transferrin (mg/dL)': 280,
        'CA125 (U/mL)': 18,
        'FBS (mg/dL)': 92,
        'Urea (mg/dL)': 22,
        'Creatinine (mg/dL)': 0.8,
        'TC': 176,
        'HDLc': 60,
        'LDLc': 95,
        'TG (mg/dL)': 110,
        'CEA': 1.8,
        'Prothrombin time': 12.0
    };

    // Clear all errors first
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => clearFieldError(input));

    const exampleCase = Math.random() < 0.5 ? malignantCase : benignCase;

    // Load example values
    Object.keys(exampleCase).forEach(fieldId => {
        const input = document.getElementById(fieldId);
        if (input) {
            input.value = exampleCase[fieldId];
        }
    });
    
    alert(exampleCase === malignantCase
        ? 'Loaded high-risk malignant example case.'
        : 'Loaded low-risk benign example case.');
}

// PDF Export Function - FIXED
function exportToPDF() {
    if (!currentPrediction) {
        alert('Please run a prediction first before exporting');
        return;
    }
    
    try {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF();
        const pageWidth = pdf.internal.pageSize.width;
        const pageHeight = pdf.internal.pageSize.height;
        let yPos = 20;
        
        // Helper function to add new page if needed
        const checkPageBreak = (requiredSpace) => {
            if (yPos + requiredSpace > pageHeight - 20) {
                pdf.addPage();
                yPos = 20;
            }
        };
        
        // Helper function to draw a professional header box
        const drawHeaderBox = () => {
            pdf.setFillColor(59, 130, 246); // Blue background
            pdf.rect(10, 10, pageWidth - 20, 35, 'F');
            pdf.setTextColor(255, 255, 255); // White text
        };
        
        // Professional Header
        drawHeaderBox();
        pdf.setFontSize(18);
        pdf.setFont("helvetica", "bold");
        pdf.text('OVARIAN CANCER RISK ASSESSMENT REPORT', pageWidth/2, 25, { align: 'center' });
        pdf.setFontSize(11);
        pdf.setFont("helvetica", "normal");
        pdf.text('AIIMS Bhubaneswar - AI-Powered Medical Decision Support System', pageWidth/2, 35, { align: 'center' });
        
        // Reset text color and position
        pdf.setTextColor(0, 0, 0);
        yPos = 60;
        
        // Report Information Box
        pdf.setFillColor(245, 247, 250);
        pdf.rect(15, yPos - 5, pageWidth - 30, 25, 'F');
        pdf.setDrawColor(59, 130, 246);
        pdf.rect(15, yPos - 5, pageWidth - 30, 25, 'S');
        
        pdf.setFontSize(10);
        pdf.setFont("helvetica", "bold");
        pdf.text('Report Generated:', 20, yPos + 5);
        pdf.setFont("helvetica", "normal");
        pdf.text(`${currentPrediction.timestamp.toLocaleDateString()} at ${currentPrediction.timestamp.toLocaleTimeString()}`, 70, yPos + 5);
        
        pdf.setFont("helvetica", "bold");
        pdf.text('Patient ID:', 20, yPos + 15);
        pdf.setFont("helvetica", "normal");
        pdf.text(`OC-${Date.now().toString().slice(-6)}`, 70, yPos + 15);
        
        yPos += 40;
        
        // Executive Summary Section
        checkPageBreak(50);
        pdf.setFillColor(59, 130, 246);
        pdf.rect(15, yPos - 3, pageWidth - 30, 12, 'F');
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(14);
        pdf.setFont("helvetica", "bold");
        pdf.text('EXECUTIVE SUMMARY', 20, yPos + 5);
        pdf.setTextColor(0, 0, 0);
        yPos += 20;
        
        const prediction = currentPrediction.prediction;
        const confidence = (prediction.confidence * 100).toFixed(1);
        const benignProb = (prediction.benignProb * 100).toFixed(1);
        const malignantProb = (prediction.malignantProb * 100).toFixed(1);
        
        // Risk Assessment Box (white background for black text)
        const riskColor = prediction.prediction === 'Malignant' ? [220, 38, 38] : [34, 197, 94];
        pdf.setFillColor(255, 255, 255);
        pdf.rect(15, yPos, pageWidth - 30, 35, 'F');
        pdf.setDrawColor(...riskColor);
        pdf.rect(15, yPos, pageWidth - 30, 35, 'S');
        
        pdf.setFontSize(16);
        pdf.setFont("helvetica", "bold");
        pdf.setTextColor(...riskColor);
        pdf.text(`PREDICTION: ${prediction.prediction.toUpperCase()}`, pageWidth/2, yPos + 12, { align: 'center' });
        
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.setFont("helvetica", "normal");
        pdf.text(`Confidence Level: ${confidence}%`, pageWidth/2, yPos + 22, { align: 'center' });
        pdf.text(`Malignant Probability: ${malignantProb}% | Benign Probability: ${benignProb}%`, pageWidth/2, yPos + 30, { align: 'center' });
        
        yPos += 50;
        
        // Save PDF
        const filename = `Ovarian_Cancer_Assessment_Report_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(filename);
        
        alert('PDF report has been generated successfully!');
        
    } catch (error) {
        console.error('PDF Export Error:', error);
        alert('PDF export failed. Please try again.');
    }
}

// Error Handling
window.addEventListener('error', function(event) {
    console.error('Application error:', event.error);
});

window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
});

// Export functions for global access
window.toggleCategory = toggleCategory;
window.clearFields = clearFields;
window.loadExampleCase = loadExampleCase;
window.exportToPDF = exportToPDF;
