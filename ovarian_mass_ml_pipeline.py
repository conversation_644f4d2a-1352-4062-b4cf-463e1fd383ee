#!/usr/bin/env python3
"""
Ovarian Mass Classification ML Pipeline
=====================================

Complete machine learning pipeline for predicting the nature of ovarian masses
as benign (0) or malignant (1) following the workflow specifications.

Author: AI Assistant
Date: 2025-08-07
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from pathlib import Path

# Core ML libraries
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer, KNNImputer
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.metrics import (
    classification_report, confusion_matrix, roc_auc_score, roc_curve,
    precision_recall_curve, f1_score, precision_score, recall_score
)

# Classical ML models
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
import xgboost as xgb
import lightgbm as lgb

# Imbalanced learning
from imblearn.over_sampling import SMOTENC
from imblearn.pipeline import Pipeline as ImbPipeline

# Feature selection and engineering
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.decomposition import PCA

# Model interpretation
import shap

# Deep learning (optional - will handle import errors gracefully)
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available. Deep learning models will be skipped.")

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

class OvarianMassClassifier:
    """
    Complete ML pipeline for ovarian mass classification following the workflow specifications.
    """
    
    def __init__(self, data_path='combined_ovarian_dataset.csv', random_state=42):
        """
        Initialize the classifier with data path and random state.
        
        Args:
            data_path (str): Path to the CSV dataset
            random_state (int): Random state for reproducibility
        """
        self.data_path = data_path
        self.random_state = random_state
        self.data = None
        self.X = None
        self.y = None
        self.X_train = None
        self.X_val = None
        self.X_test = None
        self.y_train = None
        self.y_val = None
        self.y_test = None
        self.feature_names = None
        self.categorical_features = None
        self.continuous_features = None
        self.models = {}
        self.results = {}
        
        # Set up plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
    def load_and_audit_data(self):
        """
        Task 1: Data Audit & Understanding
        Load the dataset and perform comprehensive data audit.
        """
        print("=" * 60)
        print("TASK 1: DATA AUDIT & UNDERSTANDING")
        print("=" * 60)
        
        # Load the dataset
        try:
            self.data = pd.read_csv(self.data_path)
            print(f"✓ Successfully loaded dataset from {self.data_path}")
        except FileNotFoundError:
            raise FileNotFoundError(f"Dataset not found at {self.data_path}")
        
        # Basic dataset information
        print(f"\nDataset Shape: {self.data.shape}")
        print(f"Columns: {self.data.shape[1]}")
        print(f"Rows: {self.data.shape[0]}")
        
        # Display column names and types
        print("\nColumn Information:")
        print("-" * 40)
        for i, col in enumerate(self.data.columns, 1):
            dtype = str(self.data[col].dtype)
            non_null = self.data[col].count()
            null_count = self.data[col].isnull().sum()
            print(f"{i:2d}. {col:<25} | {dtype:<10} | Non-null: {non_null:3d} | Null: {null_count:3d}")
        
        # Check for missing values
        missing_summary = self.data.isnull().sum()
        if missing_summary.sum() > 0:
            print(f"\nMissing Values Summary:")
            print(missing_summary[missing_summary > 0])
        else:
            print("\n✓ No missing values found in the dataset")
        
        # Basic statistics
        print(f"\nBasic Statistics:")
        print(self.data.describe())
        
        # Check target variable distribution
        if 'malignant' in self.data.columns and 'benign' in self.data.columns:
            malignant_count = self.data['malignant'].sum()
            benign_count = self.data['benign'].sum()
            total = len(self.data)
            
            print(f"\nTarget Variable Distribution:")
            print(f"Malignant cases: {malignant_count} ({malignant_count/total*100:.1f}%)")
            print(f"Benign cases: {benign_count} ({benign_count/total*100:.1f}%)")
            
            # Verify mutual exclusivity
            both_flags = (self.data['malignant'] == 1) & (self.data['benign'] == 1)
            neither_flags = (self.data['malignant'] == 0) & (self.data['benign'] == 0)
            
            if both_flags.sum() > 0:
                print(f"⚠️  Warning: {both_flags.sum()} cases have both malignant and benign flags set")
            if neither_flags.sum() > 0:
                print(f"⚠️  Warning: {neither_flags.sum()} cases have neither flag set")
        
        # Define feature categories based on workflow specifications
        self.continuous_features = [
            'AGE', 'HE4 (pmol/L)', 'FSH (mIU/mL)', 'Apo A1 (mg/dL)', 
            'Transferrin (mg/dL)', 'CA125 (U/mL)', 'FBS (mg/dL)', 
            'Urea (mg/dL)', 'Creatinine (mg/dL)', 'TC', 'HDLc', 'LDLc', 
            'TG (mg/dL)', 'CEA', 'Prothrombin time'
        ]
        
        self.categorical_features = [
            'USG', 'CT Scan', 'Type of Mass', 'Parity', 'Age of Menarche',
            'H/O OCP', 'Past H/O Miscarriage', 'Past H/O PCOS', 
            'Past H/O Endometriosis', 'Menopausal Status', 
            'Under Hormone Replacement Therapy', 'Undergone Tubal Ligation'
        ]
        
        print(f"\nFeature Categories:")
        print(f"Continuous features: {len(self.continuous_features)}")
        print(f"Categorical features: {len(self.categorical_features)}")
        
        return self
        
    def preprocess_data(self):
        """
        Task 2: Data Preprocessing & Cleaning
        Handle missing values, outliers, scaling, and create target variable.
        """
        print("\n" + "=" * 60)
        print("TASK 2: DATA PREPROCESSING & CLEANING")
        print("=" * 60)
        
        # Create binary target variable
        if 'malignant' in self.data.columns and 'benign' in self.data.columns:
            # Create target: 1 for malignant, 0 for benign
            self.y = self.data['malignant'].values
            print(f"✓ Created binary target variable (1=malignant, 0=benign)")
            
            # Remove target columns from features
            feature_cols = [col for col in self.data.columns 
                          if col not in ['malignant', 'benign']]
            self.X = self.data[feature_cols].copy()
        else:
            raise ValueError("Target columns 'malignant' and 'benign' not found")
        
        self.feature_names = self.X.columns.tolist()
        print(f"✓ Feature matrix shape: {self.X.shape}")
        print(f"✓ Target vector shape: {self.y.shape}")
        
        # Handle missing values (if any)
        missing_counts = self.X.isnull().sum()
        if missing_counts.sum() > 0:
            print(f"\nHandling missing values...")
            # For now, use simple imputation - can be enhanced later
            for col in self.X.columns:
                if self.X[col].isnull().sum() > 0:
                    if col in self.continuous_features:
                        self.X[col].fillna(self.X[col].median(), inplace=True)
                    else:
                        self.X[col].fillna(self.X[col].mode()[0], inplace=True)
            print(f"✓ Missing values handled")
        
        # Outlier detection for key biomarkers (CA125, HE4)
        print(f"\nOutlier Analysis:")
        key_biomarkers = ['CA125 (U/mL)', 'HE4 (pmol/L)']
        for biomarker in key_biomarkers:
            if biomarker in self.X.columns:
                Q1 = self.X[biomarker].quantile(0.25)
                Q3 = self.X[biomarker].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = ((self.X[biomarker] < lower_bound) | 
                           (self.X[biomarker] > upper_bound)).sum()
                print(f"{biomarker}: {outliers} outliers detected")
                
                # Optional: Winsorize extreme outliers (can be uncommented)
                # self.X[biomarker] = np.clip(self.X[biomarker], 
                #                            self.X[biomarker].quantile(0.01),
                #                            self.X[biomarker].quantile(0.99))
        
        print(f"✓ Data preprocessing completed")
        return self
        
    def exploratory_analysis(self):
        """
        Task 3: Exploratory Data Analysis
        Analyze class imbalance, visualize biomarkers, and create statistical summaries.
        """
        print("\n" + "=" * 60)
        print("TASK 3: EXPLORATORY DATA ANALYSIS")
        print("=" * 60)
        
        # Class distribution analysis
        unique, counts = np.unique(self.y, return_counts=True)
        class_dist = dict(zip(unique, counts))
        
        print(f"Class Distribution:")
        print(f"Benign (0): {class_dist.get(0, 0)} ({class_dist.get(0, 0)/len(self.y)*100:.1f}%)")
        print(f"Malignant (1): {class_dist.get(1, 0)} ({class_dist.get(1, 0)/len(self.y)*100:.1f}%)")
        
        imbalance_ratio = class_dist.get(0, 0) / class_dist.get(1, 0) if class_dist.get(1, 0) > 0 else float('inf')
        print(f"Imbalance Ratio (Benign:Malignant): {imbalance_ratio:.2f}:1")
        
        # Create visualizations
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Class distribution pie chart
        axes[0, 0].pie(counts, labels=['Benign', 'Malignant'], autopct='%1.1f%%', 
                       colors=['lightblue', 'lightcoral'])
        axes[0, 0].set_title('Class Distribution')
        
        # 2. Key biomarkers boxplot
        key_biomarkers = ['CA125 (U/mL)', 'HE4 (pmol/L)']
        biomarker_data = []
        labels = []
        
        for biomarker in key_biomarkers:
            if biomarker in self.X.columns:
                benign_values = self.X[self.y == 0][biomarker]
                malignant_values = self.X[self.y == 1][biomarker]
                biomarker_data.extend([benign_values, malignant_values])
                labels.extend([f'{biomarker}\n(Benign)', f'{biomarker}\n(Malignant)'])
        
        if biomarker_data:
            axes[0, 1].boxplot(biomarker_data, labels=labels)
            axes[0, 1].set_title('Key Biomarkers Distribution')
            axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. Age distribution by class
        if 'AGE' in self.X.columns:
            benign_ages = self.X[self.y == 0]['AGE']
            malignant_ages = self.X[self.y == 1]['AGE']
            
            axes[1, 0].hist(benign_ages, alpha=0.7, label='Benign', bins=20, color='lightblue')
            axes[1, 0].hist(malignant_ages, alpha=0.7, label='Malignant', bins=20, color='lightcoral')
            axes[1, 0].set_xlabel('Age')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].set_title('Age Distribution by Class')
            axes[1, 0].legend()
        
        # 4. Correlation heatmap of continuous features
        continuous_data = self.X[self.continuous_features].select_dtypes(include=[np.number])
        if not continuous_data.empty:
            corr_matrix = continuous_data.corr()
            sns.heatmap(corr_matrix, annot=False, cmap='coolwarm', center=0, 
                       ax=axes[1, 1], cbar_kws={'shrink': 0.8})
            axes[1, 1].set_title('Correlation Matrix (Continuous Features)')
        
        plt.tight_layout()
        plt.savefig('exploratory_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Statistical summary by class
        print(f"\nStatistical Summary by Class:")
        print("-" * 40)
        
        for feature in self.continuous_features[:5]:  # Show first 5 continuous features
            if feature in self.X.columns:
                benign_stats = self.X[self.y == 0][feature].describe()
                malignant_stats = self.X[self.y == 1][feature].describe()
                
                print(f"\n{feature}:")
                print(f"  Benign   - Mean: {benign_stats['mean']:.2f}, Std: {benign_stats['std']:.2f}")
                print(f"  Malignant- Mean: {malignant_stats['mean']:.2f}, Std: {malignant_stats['std']:.2f}")
        
        print(f"✓ Exploratory analysis completed")
        return self

    def feature_engineering(self):
        """
        Task 4: Feature Engineering
        Create interaction terms, clinical indices, and derived features.
        """
        print("\n" + "=" * 60)
        print("TASK 4: FEATURE ENGINEERING")
        print("=" * 60)

        # Create a copy for feature engineering
        X_engineered = self.X.copy()

        # 1. Interaction terms
        print("Creating interaction terms...")

        # CA125 × HE4 interaction (important for ovarian cancer)
        if 'CA125 (U/mL)' in X_engineered.columns and 'HE4 (pmol/L)' in X_engineered.columns:
            X_engineered['CA125_HE4_interaction'] = (X_engineered['CA125 (U/mL)'] *
                                                    X_engineered['HE4 (pmol/L)'])
            print("✓ Created CA125 × HE4 interaction term")

        # Age × Biomarker interactions
        if 'AGE' in X_engineered.columns:
            for biomarker in ['CA125 (U/mL)', 'HE4 (pmol/L)']:
                if biomarker in X_engineered.columns:
                    interaction_name = f'AGE_{biomarker.split()[0]}_interaction'
                    X_engineered[interaction_name] = X_engineered['AGE'] * X_engineered[biomarker]
                    print(f"✓ Created {interaction_name}")

        # 2. Clinical indices
        print("\nCreating clinical indices...")

        # ROMA Score approximation (Risk of Ovarian Malignancy Algorithm)
        # Simplified version - actual ROMA requires specific formulas
        if ('CA125 (U/mL)' in X_engineered.columns and
            'HE4 (pmol/L)' in X_engineered.columns and
            'Menopausal Status' in X_engineered.columns):

            # Log transform biomarkers for ROMA calculation
            log_ca125 = np.log(X_engineered['CA125 (U/mL)'] + 1)
            log_he4 = np.log(X_engineered['HE4 (pmol/L)'] + 1)

            # Simplified ROMA-like score
            X_engineered['ROMA_like_score'] = log_ca125 + log_he4 + X_engineered['Menopausal Status']
            print("✓ Created ROMA-like clinical score")

        # 3. Ratio features
        print("\nCreating ratio features...")

        # Lipid ratios
        if 'TC' in X_engineered.columns and 'HDLc' in X_engineered.columns:
            X_engineered['TC_HDL_ratio'] = X_engineered['TC'] / (X_engineered['HDLc'] + 1e-6)
            print("✓ Created TC/HDL ratio")

        if 'LDLc' in X_engineered.columns and 'HDLc' in X_engineered.columns:
            X_engineered['LDL_HDL_ratio'] = X_engineered['LDLc'] / (X_engineered['HDLc'] + 1e-6)
            print("✓ Created LDL/HDL ratio")

        # Biomarker ratios
        if 'CA125 (U/mL)' in X_engineered.columns and 'CEA' in X_engineered.columns:
            X_engineered['CA125_CEA_ratio'] = (X_engineered['CA125 (U/mL)'] /
                                              (X_engineered['CEA'] + 1e-6))
            print("✓ Created CA125/CEA ratio")

        # 4. Polynomial features for key biomarkers
        print("\nCreating polynomial features...")

        key_biomarkers = ['CA125 (U/mL)', 'HE4 (pmol/L)']
        for biomarker in key_biomarkers:
            if biomarker in X_engineered.columns:
                # Square and cube terms
                biomarker_clean = biomarker.split()[0]  # Get clean name
                X_engineered[f'{biomarker_clean}_squared'] = X_engineered[biomarker] ** 2
                X_engineered[f'{biomarker_clean}_cubed'] = X_engineered[biomarker] ** 3
                print(f"✓ Created polynomial features for {biomarker}")

        # 5. Binned categorical features from continuous
        print("\nCreating binned features...")

        # Age groups
        if 'AGE' in X_engineered.columns:
            X_engineered['Age_group'] = pd.cut(X_engineered['AGE'],
                                              bins=[0, 30, 40, 50, 60, 100],
                                              labels=[0, 1, 2, 3, 4])
            print("✓ Created age groups")

        # CA125 risk categories (based on clinical thresholds)
        if 'CA125 (U/mL)' in X_engineered.columns:
            X_engineered['CA125_risk_category'] = pd.cut(X_engineered['CA125 (U/mL)'],
                                                        bins=[0, 35, 200, 1000, float('inf')],
                                                        labels=[0, 1, 2, 3])  # Low, Moderate, High, Very High
            print("✓ Created CA125 risk categories")

        # Update feature lists
        new_features = [col for col in X_engineered.columns if col not in self.X.columns]
        print(f"\n✓ Created {len(new_features)} new features:")
        for feature in new_features:
            print(f"  - {feature}")

        # Update the feature matrix
        self.X = X_engineered
        self.feature_names = self.X.columns.tolist()

        print(f"✓ Feature engineering completed. New shape: {self.X.shape}")
        return self

    def split_data(self):
        """
        Task 5: Data Splitting Strategy
        Implement stratified train/validation/test split (60%/20%/20%).
        """
        print("\n" + "=" * 60)
        print("TASK 5: DATA SPLITTING STRATEGY")
        print("=" * 60)

        # First split: 80% train+val, 20% test
        X_temp, self.X_test, y_temp, self.y_test = train_test_split(
            self.X, self.y, test_size=0.2, random_state=self.random_state,
            stratify=self.y
        )

        # Second split: 60% train, 20% val (from the 80%)
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            X_temp, y_temp, test_size=0.25, random_state=self.random_state,  # 0.25 * 0.8 = 0.2
            stratify=y_temp
        )

        # Print split information
        print(f"Dataset split completed:")
        print(f"  Training set:   {self.X_train.shape[0]} samples ({self.X_train.shape[0]/len(self.X)*100:.1f}%)")
        print(f"  Validation set: {self.X_val.shape[0]} samples ({self.X_val.shape[0]/len(self.X)*100:.1f}%)")
        print(f"  Test set:       {self.X_test.shape[0]} samples ({self.X_test.shape[0]/len(self.X)*100:.1f}%)")

        # Check class distribution in each split
        splits = [
            ("Training", self.y_train),
            ("Validation", self.y_val),
            ("Test", self.y_test)
        ]

        print(f"\nClass distribution across splits:")
        for split_name, y_split in splits:
            unique, counts = np.unique(y_split, return_counts=True)
            class_dist = dict(zip(unique, counts))
            benign_pct = class_dist.get(0, 0) / len(y_split) * 100
            malignant_pct = class_dist.get(1, 0) / len(y_split) * 100
            print(f"  {split_name:10}: Benign {benign_pct:.1f}%, Malignant {malignant_pct:.1f}%")

        print(f"✓ Stratified data splitting completed")
        return self

    def train_classical_models(self):
        """
        Task 6: Baseline & Classical ML Models
        Implement and evaluate Logistic Regression, Random Forest, XGBoost, and SVM.
        """
        print("\n" + "=" * 60)
        print("TASK 6: BASELINE & CLASSICAL ML MODELS")
        print("=" * 60)

        # Prepare preprocessing pipelines
        # For tree-based models (no scaling needed)
        tree_preprocessor = ColumnTransformer(
            transformers=[
                ('num', SimpleImputer(strategy='median'),
                 [i for i, col in enumerate(self.X_train.columns) if col in self.continuous_features]),
                ('cat', SimpleImputer(strategy='most_frequent'),
                 [i for i, col in enumerate(self.X_train.columns) if col in self.categorical_features])
            ],
            remainder='passthrough'
        )

        # For distance-based models (scaling needed)
        scaled_preprocessor = ColumnTransformer(
            transformers=[
                ('num', Pipeline([
                    ('imputer', SimpleImputer(strategy='median')),
                    ('scaler', StandardScaler())
                ]), [i for i, col in enumerate(self.X_train.columns) if col in self.continuous_features]),
                ('cat', SimpleImputer(strategy='most_frequent'),
                 [i for i, col in enumerate(self.X_train.columns) if col in self.categorical_features])
            ],
            remainder='passthrough'
        )

        # Define models with their preprocessing needs
        model_configs = {
            'Logistic Regression': {
                'model': LogisticRegression(random_state=self.random_state, max_iter=1000,
                                          class_weight='balanced'),
                'preprocessor': scaled_preprocessor,
                'needs_scaling': True
            },
            'Random Forest': {
                'model': RandomForestClassifier(n_estimators=100, random_state=self.random_state,
                                              class_weight='balanced', n_jobs=-1),
                'preprocessor': tree_preprocessor,
                'needs_scaling': False
            },
            'XGBoost': {
                'model': xgb.XGBClassifier(random_state=self.random_state, eval_metric='logloss',
                                         scale_pos_weight=len(self.y_train[self.y_train==0])/len(self.y_train[self.y_train==1])),
                'preprocessor': tree_preprocessor,
                'needs_scaling': False
            },
            'LightGBM': {
                'model': lgb.LGBMClassifier(random_state=self.random_state, verbose=-1,
                                          class_weight='balanced'),
                'preprocessor': tree_preprocessor,
                'needs_scaling': False
            },
            'SVM': {
                'model': SVC(random_state=self.random_state, probability=True,
                           class_weight='balanced'),
                'preprocessor': scaled_preprocessor,
                'needs_scaling': True
            }
        }

        # Train and evaluate each model
        for model_name, config in model_configs.items():
            print(f"\nTraining {model_name}...")

            try:
                # Create pipeline
                pipeline = Pipeline([
                    ('preprocessor', config['preprocessor']),
                    ('classifier', config['model'])
                ])

                # Train the model
                pipeline.fit(self.X_train, self.y_train)

                # Make predictions
                y_train_pred = pipeline.predict(self.X_train)
                y_val_pred = pipeline.predict(self.X_val)
                y_train_proba = pipeline.predict_proba(self.X_train)[:, 1]
                y_val_proba = pipeline.predict_proba(self.X_val)[:, 1]

                # Calculate metrics
                train_auc = roc_auc_score(self.y_train, y_train_proba)
                val_auc = roc_auc_score(self.y_val, y_val_proba)
                val_f1 = f1_score(self.y_val, y_val_pred)
                val_precision = precision_score(self.y_val, y_val_pred)
                val_recall = recall_score(self.y_val, y_val_pred)

                # Store results
                self.models[model_name] = pipeline
                self.results[model_name] = {
                    'train_auc': train_auc,
                    'val_auc': val_auc,
                    'val_f1': val_f1,
                    'val_precision': val_precision,
                    'val_recall': val_recall,
                    'y_val_pred': y_val_pred,
                    'y_val_proba': y_val_proba
                }

                print(f"  ✓ Train AUC: {train_auc:.4f}")
                print(f"  ✓ Val AUC: {val_auc:.4f}")
                print(f"  ✓ Val F1: {val_f1:.4f}")
                print(f"  ✓ Val Precision: {val_precision:.4f}")
                print(f"  ✓ Val Recall (Sensitivity): {val_recall:.4f}")

            except Exception as e:
                print(f"  ❌ Error training {model_name}: {str(e)}")
                continue

        # Cross-validation for more robust evaluation
        print(f"\nPerforming 5-fold cross-validation...")
        cv_results = {}

        for model_name, config in model_configs.items():
            if model_name in self.models:  # Only for successfully trained models
                try:
                    pipeline = Pipeline([
                        ('preprocessor', config['preprocessor']),
                        ('classifier', config['model'])
                    ])

                    # Combine train and validation for CV
                    X_train_val = pd.concat([self.X_train, self.X_val])
                    y_train_val = np.concatenate([self.y_train, self.y_val])

                    cv_scores = cross_val_score(pipeline, X_train_val, y_train_val,
                                              cv=StratifiedKFold(n_splits=5, shuffle=True,
                                                               random_state=self.random_state),
                                              scoring='roc_auc', n_jobs=-1)

                    cv_results[model_name] = {
                        'mean_cv_auc': cv_scores.mean(),
                        'std_cv_auc': cv_scores.std()
                    }

                    print(f"  {model_name}: CV AUC = {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

                except Exception as e:
                    print(f"  ❌ CV error for {model_name}: {str(e)}")

        # Update results with CV scores
        for model_name, cv_result in cv_results.items():
            if model_name in self.results:
                self.results[model_name].update(cv_result)

        print(f"✓ Classical models training completed")
        return self

    def train_deep_learning_models(self):
        """
        Task 7: Deep Learning Models
        Implement MLP with proper handling of categorical and continuous features.
        """
        print("\n" + "=" * 60)
        print("TASK 7: DEEP LEARNING MODELS")
        print("=" * 60)

        if not TORCH_AVAILABLE:
            print("❌ PyTorch not available. Skipping deep learning models.")
            return self

        # Prepare data for deep learning
        print("Preparing data for deep learning...")

        # Scale continuous features
        scaler = StandardScaler()
        X_train_scaled = self.X_train.copy()
        X_val_scaled = self.X_val.copy()

        # Identify numeric columns for scaling
        numeric_cols = self.X_train.select_dtypes(include=[np.number]).columns
        X_train_scaled[numeric_cols] = scaler.fit_transform(self.X_train[numeric_cols])
        X_val_scaled[numeric_cols] = scaler.transform(self.X_val[numeric_cols])

        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled.values)
        X_val_tensor = torch.FloatTensor(X_val_scaled.values)
        y_train_tensor = torch.FloatTensor(self.y_train)
        y_val_tensor = torch.FloatTensor(self.y_val)

        # Create data loaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

        # Define MLP architecture
        class MLPClassifier(nn.Module):
            def __init__(self, input_dim, hidden_dims=[128, 64, 32], dropout_rate=0.3):
                super(MLPClassifier, self).__init__()

                layers = []
                prev_dim = input_dim

                for hidden_dim in hidden_dims:
                    layers.extend([
                        nn.Linear(prev_dim, hidden_dim),
                        nn.BatchNorm1d(hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate)
                    ])
                    prev_dim = hidden_dim

                layers.append(nn.Linear(prev_dim, 1))
                layers.append(nn.Sigmoid())

                self.network = nn.Sequential(*layers)

            def forward(self, x):
                return self.network(x).squeeze()

        # Initialize model
        input_dim = X_train_tensor.shape[1]
        model = MLPClassifier(input_dim)

        # Calculate class weights for imbalanced data
        pos_weight = torch.FloatTensor([len(self.y_train[self.y_train==0])/len(self.y_train[self.y_train==1])])
        criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

        # Training loop
        print("Training MLP model...")
        num_epochs = 100
        best_val_auc = 0
        patience = 15
        patience_counter = 0

        train_losses = []
        val_aucs = []

        for epoch in range(num_epochs):
            # Training phase
            model.train()
            train_loss = 0
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                train_loss += loss.item()

            # Validation phase
            model.eval()
            val_predictions = []
            val_probabilities = []

            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    outputs = model(batch_X)
                    val_probabilities.extend(outputs.cpu().numpy())
                    val_predictions.extend((outputs > 0.5).cpu().numpy())

            # Calculate metrics
            val_auc = roc_auc_score(self.y_val, val_probabilities)
            val_aucs.append(val_auc)
            train_losses.append(train_loss / len(train_loader))

            scheduler.step(val_auc)

            # Early stopping
            if val_auc > best_val_auc:
                best_val_auc = val_auc
                patience_counter = 0
                # Save best model state
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1

            if epoch % 20 == 0:
                print(f"  Epoch {epoch}: Train Loss = {train_losses[-1]:.4f}, Val AUC = {val_auc:.4f}")

            if patience_counter >= patience:
                print(f"  Early stopping at epoch {epoch}")
                break

        # Load best model
        model.load_state_dict(best_model_state)

        # Final evaluation
        model.eval()
        with torch.no_grad():
            train_proba = model(X_train_tensor).cpu().numpy()
            val_proba = model(X_val_tensor).cpu().numpy()
            train_pred = (train_proba > 0.5).astype(int)
            val_pred = (val_proba > 0.5).astype(int)

        # Calculate final metrics
        train_auc = roc_auc_score(self.y_train, train_proba)
        val_auc = roc_auc_score(self.y_val, val_proba)
        val_f1 = f1_score(self.y_val, val_pred)
        val_precision = precision_score(self.y_val, val_pred)
        val_recall = recall_score(self.y_val, val_pred)

        # Store results
        self.models['MLP'] = {
            'model': model,
            'scaler': scaler
        }
        self.results['MLP'] = {
            'train_auc': train_auc,
            'val_auc': val_auc,
            'val_f1': val_f1,
            'val_precision': val_precision,
            'val_recall': val_recall,
            'y_val_pred': val_pred,
            'y_val_proba': val_proba
        }

        print(f"  ✓ Train AUC: {train_auc:.4f}")
        print(f"  ✓ Val AUC: {val_auc:.4f}")
        print(f"  ✓ Val F1: {val_f1:.4f}")
        print(f"  ✓ Val Precision: {val_precision:.4f}")
        print(f"  ✓ Val Recall (Sensitivity): {val_recall:.4f}")

        print(f"✓ Deep learning models training completed")
        return self

    def evaluate_models(self):
        """
        Task 9: Model Evaluation & Validation
        Comprehensive evaluation using multiple metrics and visualizations.
        """
        print("\n" + "=" * 60)
        print("TASK 9: MODEL EVALUATION & VALIDATION")
        print("=" * 60)

        if not self.results:
            print("❌ No models to evaluate. Please train models first.")
            return self

        # Create evaluation summary
        print("Model Performance Summary:")
        print("-" * 80)
        print(f"{'Model':<15} {'Train AUC':<10} {'Val AUC':<10} {'Val F1':<8} {'Precision':<10} {'Recall':<8}")
        print("-" * 80)

        for model_name, results in self.results.items():
            print(f"{model_name:<15} {results['train_auc']:<10.4f} {results['val_auc']:<10.4f} "
                  f"{results['val_f1']:<8.4f} {results['val_precision']:<10.4f} {results['val_recall']:<8.4f}")

        # Find best model based on validation AUC
        best_model_name = max(self.results.keys(), key=lambda x: self.results[x]['val_auc'])
        best_auc = self.results[best_model_name]['val_auc']
        best_sensitivity = self.results[best_model_name]['val_recall']

        print(f"\n🏆 Best Model: {best_model_name}")
        print(f"   Validation AUC: {best_auc:.4f}")
        print(f"   Sensitivity: {best_sensitivity:.4f}")

        # Check if sensitivity requirement is met
        if best_sensitivity >= 0.85:
            print(f"   ✓ Meets sensitivity requirement (≥ 0.85)")
        else:
            print(f"   ⚠️  Does not meet sensitivity requirement (≥ 0.85)")

        # Create comprehensive evaluation plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. ROC Curves
        for model_name, results in self.results.items():
            if 'y_val_proba' in results:
                fpr, tpr, _ = roc_curve(self.y_val, results['y_val_proba'])
                auc_score = results['val_auc']
                axes[0, 0].plot(fpr, tpr, label=f'{model_name} (AUC = {auc_score:.3f})')

        axes[0, 0].plot([0, 1], [0, 1], 'k--', alpha=0.5)
        axes[0, 0].set_xlabel('False Positive Rate')
        axes[0, 0].set_ylabel('True Positive Rate')
        axes[0, 0].set_title('ROC Curves')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. Precision-Recall Curves
        for model_name, results in self.results.items():
            if 'y_val_proba' in results:
                precision, recall, _ = precision_recall_curve(self.y_val, results['y_val_proba'])
                axes[0, 1].plot(recall, precision, label=f'{model_name}')

        axes[0, 1].set_xlabel('Recall')
        axes[0, 1].set_ylabel('Precision')
        axes[0, 1].set_title('Precision-Recall Curves')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. Model Comparison Bar Chart
        model_names = list(self.results.keys())
        val_aucs = [self.results[name]['val_auc'] for name in model_names]

        bars = axes[1, 0].bar(model_names, val_aucs, color='skyblue', alpha=0.7)
        axes[1, 0].set_ylabel('Validation AUC')
        axes[1, 0].set_title('Model Comparison (Validation AUC)')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, auc in zip(bars, val_aucs):
            axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{auc:.3f}', ha='center', va='bottom')

        # 4. Confusion Matrix for Best Model
        if best_model_name in self.results and 'y_val_pred' in self.results[best_model_name]:
            cm = confusion_matrix(self.y_val, self.results[best_model_name]['y_val_pred'])
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, 1])
            axes[1, 1].set_xlabel('Predicted')
            axes[1, 1].set_ylabel('Actual')
            axes[1, 1].set_title(f'Confusion Matrix - {best_model_name}')

        plt.tight_layout()
        plt.savefig('model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✓ Model evaluation completed")
        return self

    def create_ensemble(self):
        """
        Task 11: Ensemble Methods
        Create ensemble models combining best performing approaches.
        """
        print("\n" + "=" * 60)
        print("TASK 11: ENSEMBLE METHODS")
        print("=" * 60)

        if len(self.models) < 2:
            print("❌ Need at least 2 models for ensemble. Skipping ensemble creation.")
            return self

        # Select top 3 models based on validation AUC
        sorted_models = sorted(self.results.items(), key=lambda x: x[1]['val_auc'], reverse=True)
        top_models = sorted_models[:min(3, len(sorted_models))]

        print(f"Creating ensemble from top {len(top_models)} models:")
        for i, (model_name, results) in enumerate(top_models, 1):
            print(f"  {i}. {model_name} (Val AUC: {results['val_auc']:.4f})")

        # Simple averaging ensemble
        ensemble_proba = np.zeros(len(self.y_val))
        ensemble_train_proba = np.zeros(len(self.y_train))

        for model_name, _ in top_models:
            if model_name in self.results and 'y_val_proba' in self.results[model_name]:
                ensemble_proba += self.results[model_name]['y_val_proba']

                # Get training predictions for ensemble
                if model_name == 'MLP':
                    # Handle MLP separately
                    model_info = self.models[model_name]
                    model = model_info['model']
                    scaler = model_info['scaler']

                    X_train_scaled = self.X_train.copy()
                    numeric_cols = self.X_train.select_dtypes(include=[np.number]).columns
                    X_train_scaled[numeric_cols] = scaler.transform(self.X_train[numeric_cols])

                    if TORCH_AVAILABLE:
                        X_train_tensor = torch.FloatTensor(X_train_scaled.values)
                        model.eval()
                        with torch.no_grad():
                            train_proba = model(X_train_tensor).cpu().numpy()
                        ensemble_train_proba += train_proba
                else:
                    # Handle sklearn models
                    pipeline = self.models[model_name]
                    train_proba = pipeline.predict_proba(self.X_train)[:, 1]
                    ensemble_train_proba += train_proba

        # Average the predictions
        ensemble_proba /= len(top_models)
        ensemble_train_proba /= len(top_models)
        ensemble_pred = (ensemble_proba > 0.5).astype(int)

        # Calculate ensemble metrics
        train_auc = roc_auc_score(self.y_train, ensemble_train_proba)
        val_auc = roc_auc_score(self.y_val, ensemble_proba)
        val_f1 = f1_score(self.y_val, ensemble_pred)
        val_precision = precision_score(self.y_val, ensemble_pred)
        val_recall = recall_score(self.y_val, ensemble_pred)

        # Store ensemble results
        self.results['Ensemble'] = {
            'train_auc': train_auc,
            'val_auc': val_auc,
            'val_f1': val_f1,
            'val_precision': val_precision,
            'val_recall': val_recall,
            'y_val_pred': ensemble_pred,
            'y_val_proba': ensemble_proba
        }

        print(f"\nEnsemble Performance:")
        print(f"  ✓ Train AUC: {train_auc:.4f}")
        print(f"  ✓ Val AUC: {val_auc:.4f}")
        print(f"  ✓ Val F1: {val_f1:.4f}")
        print(f"  ✓ Val Precision: {val_precision:.4f}")
        print(f"  ✓ Val Recall (Sensitivity): {val_recall:.4f}")

        print(f"✓ Ensemble methods completed")
        return self

    def final_evaluation_on_test_set(self):
        """
        Task 12: Final Model Selection & Export
        Evaluate all models on the held-out test set and display comprehensive performance table.
        """
        print("\n" + "=" * 60)
        print("TASK 12: FINAL EVALUATION ON TEST SET")
        print("=" * 60)

        # Evaluate all models on test set
        test_results = {}
        
        for model_name in self.results.keys():
            try:
                if model_name == 'MLP':
                    # Handle MLP
                    model_info = self.models[model_name]
                    model = model_info['model']
                    scaler = model_info['scaler']

                    X_test_scaled = self.X_test.copy()
                    numeric_cols = self.X_test.select_dtypes(include=[np.number]).columns
                    X_test_scaled[numeric_cols] = scaler.transform(self.X_test[numeric_cols])

                    if TORCH_AVAILABLE:
                        X_test_tensor = torch.FloatTensor(X_test_scaled.values)
                        model.eval()
                        with torch.no_grad():
                            test_proba = model(X_test_tensor).cpu().numpy()
                        test_pred = (test_proba > 0.5).astype(int)
                    else:
                        continue
                        
                elif model_name == 'Ensemble':
                    # Handle ensemble predictions
                    sorted_models = sorted(self.results.items(), key=lambda x: x[1]['val_auc'], reverse=True)
                    top_models = sorted_models[:min(3, len(sorted_models))]
                    
                    ensemble_proba = np.zeros(len(self.y_test))
                    valid_predictions = 0
                    
                    for top_model_name, _ in top_models:
                        if top_model_name == 'MLP' and top_model_name in self.models:
                            model_info = self.models[top_model_name]
                            model = model_info['model']
                            scaler = model_info['scaler']

                            X_test_scaled = self.X_test.copy()
                            numeric_cols = self.X_test.select_dtypes(include=[np.number]).columns
                            X_test_scaled[numeric_cols] = scaler.transform(self.X_test[numeric_cols])

                            if TORCH_AVAILABLE:
                                X_test_tensor = torch.FloatTensor(X_test_scaled.values)
                                model.eval()
                                with torch.no_grad():
                                    proba = model(X_test_tensor).cpu().numpy()
                                ensemble_proba += proba
                                valid_predictions += 1
                        elif top_model_name in self.models and top_model_name != 'Ensemble':
                            pipeline = self.models[top_model_name]
                            proba = pipeline.predict_proba(self.X_test)[:, 1]
                            ensemble_proba += proba
                            valid_predictions += 1
                    
                    if valid_predictions > 0:
                        test_proba = ensemble_proba / valid_predictions
                        test_pred = (test_proba > 0.5).astype(int)
                    else:
                        continue
                        
                else:
                    # Handle sklearn models
                    if model_name in self.models:
                        pipeline = self.models[model_name]
                        test_pred = pipeline.predict(self.X_test)
                        test_proba = pipeline.predict_proba(self.X_test)[:, 1]
                    else:
                        continue

                # Calculate test metrics
                test_auc = roc_auc_score(self.y_test, test_proba)
                test_f1 = f1_score(self.y_test, test_pred)
                test_precision = precision_score(self.y_test, test_pred)
                test_recall = recall_score(self.y_test, test_pred)
                
                # Calculate additional clinical metrics
                cm = confusion_matrix(self.y_test, test_pred)
                tn, fp, fn, tp = cm.ravel()
                specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
                npv = tn / (tn + fn) if (tn + fn) > 0 else 0
                
                test_results[model_name] = {
                    'test_auc': test_auc,
                    'test_f1': test_f1,
                    'test_precision': test_precision,
                    'test_recall': test_recall,
                    'test_specificity': specificity,
                    'test_npv': npv,
                    'test_pred': test_pred,
                    'test_proba': test_proba,
                    'confusion_matrix': cm
                }
                
                print(f"✓ {model_name} evaluated on test set")
                
            except Exception as e:
                print(f"❌ Error evaluating {model_name} on test set: {str(e)}")
                continue

        # Display comprehensive performance table
        print(f"\n" + "=" * 120)
        print("COMPREHENSIVE MODEL PERFORMANCE COMPARISON - TEST SET")
        print("=" * 120)
        
        # Validation vs Test Performance Table
        print(f"\nValidation vs Test Performance Comparison:")
        print("-" * 120)
        header = f"{'Model':<15} {'Val AUC':<8} {'Test AUC':<9} {'Val F1':<8} {'Test F1':<9} {'Val Sens':<9} {'Test Sens':<10} {'Val Spec':<9} {'Test Spec':<10}"
        print(header)
        print("-" * 120)
        
        for model_name in self.results.keys():
            if model_name in test_results:
                val_results = self.results[model_name]
                test_res = test_results[model_name]
                
                # Calculate validation specificity if not stored
                if 'val_specificity' not in val_results:
                    if 'y_val_pred' in val_results:
                        cm_val = confusion_matrix(self.y_val, val_results['y_val_pred'])
                        tn_val, fp_val, fn_val, tp_val = cm_val.ravel()
                        val_specificity = tn_val / (tn_val + fp_val) if (tn_val + fp_val) > 0 else 0
                    else:
                        val_specificity = 0.0
                else:
                    val_specificity = val_results['val_specificity']
                
                print(f"{model_name:<15} {val_results['val_auc']:<8.4f} {test_res['test_auc']:<9.4f} "
                      f"{val_results['val_f1']:<8.4f} {test_res['test_f1']:<9.4f} "
                      f"{val_results['val_recall']:<9.4f} {test_res['test_recall']:<10.4f} "
                      f"{val_specificity:<9.4f} {test_res['test_specificity']:<10.4f}")

        # Detailed Test Set Performance Table
        print(f"\nDetailed Test Set Performance Metrics:")
        print("-" * 100)
        header2 = f"{'Model':<15} {'AUC':<8} {'F1':<8} {'Precision':<10} {'Recall':<8} {'Specificity':<12} {'NPV':<8}"
        print(header2)
        print("-" * 100)
        
        for model_name, test_res in test_results.items():
            print(f"{model_name:<15} {test_res['test_auc']:<8.4f} {test_res['test_f1']:<8.4f} "
                  f"{test_res['test_precision']:<10.4f} {test_res['test_recall']:<8.4f} "
                  f"{test_res['test_specificity']:<12.4f} {test_res['test_npv']:<8.4f}")

        # Select the best model based on test AUC and sensitivity
        best_model_name = None
        best_score = 0

        for model_name, test_res in test_results.items():
            # Prioritize models with sensitivity >= 0.85, then by AUC
            if test_res['test_recall'] >= 0.85:
                score = test_res['test_auc']
            else:
                score = test_res['test_auc'] * 0.8  # Penalty for low sensitivity

            if score > best_score:
                best_score = score
                best_model_name = model_name

        if best_model_name is None:
            best_model_name = max(test_results.keys(), key=lambda x: test_results[x]['test_auc'])

        print(f"\n� BEST MODEL ON TEST SET: {best_model_name}")
        print("=" * 50)
        best_test_res = test_results[best_model_name]
        print(f"  �🎯 Test AUC: {best_test_res['test_auc']:.4f}")
        print(f"  🎯 Test F1: {best_test_res['test_f1']:.4f}")
        print(f"  🎯 Test Precision (PPV): {best_test_res['test_precision']:.4f}")
        print(f"  🎯 Test Recall (Sensitivity): {best_test_res['test_recall']:.4f}")
        print(f"  🎯 Test Specificity: {best_test_res['test_specificity']:.4f}")
        print(f"  🎯 Test NPV: {best_test_res['test_npv']:.4f}")

        # Classification report for best model
        print(f"\nDetailed Classification Report for {best_model_name}:")
        print(classification_report(self.y_test, best_test_res['test_pred'], target_names=['Benign', 'Malignant']))

        # Confusion matrix for best model
        cm = best_test_res['confusion_matrix']
        print(f"\nConfusion Matrix for {best_model_name}:")
        print(f"                Predicted")
        print(f"Actual    Benign  Malignant")
        print(f"Benign      {cm[0,0]:3d}      {cm[0,1]:3d}")
        print(f"Malignant   {cm[1,0]:3d}      {cm[1,1]:3d}")

        # Check clinical requirements
        if best_test_res['test_recall'] >= 0.85:
            print(f"\n✅ Best model meets sensitivity requirement (≥ 0.85)")
        else:
            print(f"\n⚠️  Best model does not meet sensitivity requirement (≥ 0.85)")

        # Store test results for later use
        self.test_results = test_results

        print(f"\n✓ Final evaluation on test set completed")
        print(f"📊 {len(test_results)} models evaluated on test set")
        return self

    def save_performance_summary(self):
        """
        Save comprehensive performance summary to CSV file.
        """
        if not hasattr(self, 'test_results') or not self.test_results:
            print("⚠️  No test results available to save. Please run final_evaluation_on_test_set first.")
            return self
            
        # Create performance summary dataframe
        performance_data = []
        
        for model_name in self.results.keys():
            if model_name in self.test_results:
                val_results = self.results[model_name]
                test_res = self.test_results[model_name]
                
                # Calculate validation specificity
                if 'y_val_pred' in val_results:
                    cm_val = confusion_matrix(self.y_val, val_results['y_val_pred'])
                    tn_val, fp_val, fn_val, tp_val = cm_val.ravel()
                    val_specificity = tn_val / (tn_val + fp_val) if (tn_val + fp_val) > 0 else 0
                    val_npv = tn_val / (tn_val + fn_val) if (tn_val + fn_val) > 0 else 0
                else:
                    val_specificity = 0.0
                    val_npv = 0.0
                
                row = {
                    'Model': model_name,
                    'Validation_AUC': val_results['val_auc'],
                    'Validation_F1': val_results['val_f1'],
                    'Validation_Precision': val_results['val_precision'],
                    'Validation_Recall': val_results['val_recall'],
                    'Validation_Specificity': val_specificity,
                    'Validation_NPV': val_npv,
                    'Test_AUC': test_res['test_auc'],
                    'Test_F1': test_res['test_f1'],
                    'Test_Precision': test_res['test_precision'],
                    'Test_Recall': test_res['test_recall'],
                    'Test_Specificity': test_res['test_specificity'],
                    'Test_NPV': test_res['test_npv'],
                    'AUC_Difference': test_res['test_auc'] - val_results['val_auc'],
                    'Sensitivity_Difference': test_res['test_recall'] - val_results['val_recall']
                }
                performance_data.append(row)
        
        # Create DataFrame and save
        performance_df = pd.DataFrame(performance_data)
        performance_df = performance_df.round(4)
        
        # Save to CSV
        csv_filename = 'model_performance_summary.csv'
        performance_df.to_csv(csv_filename, index=False)
        
        print(f"\n✅ Performance summary saved to: {csv_filename}")
        print(f"📄 Summary includes {len(performance_data)} models with validation and test metrics")
        
        return self

    def run_complete_pipeline(self):
        """
        Execute the complete machine learning pipeline.
        """
        print("🚀 STARTING OVARIAN MASS CLASSIFICATION ML PIPELINE")
        print("=" * 80)

        try:
            # Execute all pipeline steps
            self.load_and_audit_data()

            # Update task progress
            self.preprocess_data()
            self.exploratory_analysis()
            self.feature_engineering()
            self.split_data()
            self.train_classical_models()
            self.train_deep_learning_models()
            self.evaluate_models()
            self.create_ensemble()
            self.final_evaluation_on_test_set()
            self.save_performance_summary()

            print("\n" + "=" * 80)
            print("🎉 PIPELINE COMPLETED SUCCESSFULLY!")
            print("=" * 80)

            # Summary of results
            print(f"\nFinal Results Summary:")
            print(f"  📊 Dataset: {len(self.X)} samples, {len(self.feature_names)} features")
            print(f"  🤖 Models trained: {len(self.models)}")
            print(f"  🏆 Best model: {max(self.results.keys(), key=lambda x: self.results[x]['val_auc'])}")
            print(f"  📈 Best validation AUC: {max(self.results.values(), key=lambda x: x['val_auc'])['val_auc']:.4f}")

            return self

        except Exception as e:
            print(f"❌ Pipeline failed with error: {str(e)}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """
    Main execution function.
    """
    # Initialize and run the pipeline
    classifier = OvarianMassClassifier(
        data_path='combined_ovarian_dataset.csv',
        random_state=42
    )

    # Run the complete pipeline
    result = classifier.run_complete_pipeline()

    if result is not None:
        print("\n✅ Pipeline execution completed successfully!")
        print("📁 Generated files:")
        print("  - exploratory_analysis.png")
        print("  - model_evaluation.png")
        print("  - model_performance_summary.csv")
    else:
        print("\n❌ Pipeline execution failed!")


if __name__ == "__main__":
    main()
